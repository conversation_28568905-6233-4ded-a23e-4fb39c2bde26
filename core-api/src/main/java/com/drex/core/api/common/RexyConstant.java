package com.drex.core.api.common;

import lombok.Data;
import lombok.Getter;

@Data
public class RexyConstant {

    public enum RexyBasketsTypeEnum {
        /**
         * 普通篮子
         */
        normal,
        /**
         * 邀请篮子
         */
        invite,

        ;
    }

    public enum OperateTypeEnum {
        /**
         * 收集
         */
        collect,
        /**
         * 取出
         */
        claim,

        ;
    }

    @Getter
    public enum CommonStatus {
        PROCESSING("PROCESSING", 0),
        SUCCESS("SUCCESS", 1),
        FAIL("FAIL", 2),
        TRYING("TRYING", 3),
        ;

        final String code;
        final int value;

        CommonStatus(String code, int value) {
            this.code = code;
            this.value = value;
        }
    }

    @Getter
    public enum CustomerRexyStatus {
        ACTIVE,
        DISABLE,
        ;
    }

    public static String STATISTICS_ORIGINAL_DATA = "STATISTICS_ORIGINAL_DATA";

    @Getter
    public enum RiskMonitorType {
        X_REPLY, // X 回复
        YOUTUBE_WATCH, // YouTube 观看
        CLAIM_POINT, // 积分领取
        ;
    }

    @Getter
    public enum MaizeStatus {
        EXPIRED, //奖励已过期
        CLAIMED, //奖励已领取
        ISSUED,//已发放
        UNCLAIMED, //奖励未领取
    }
}
