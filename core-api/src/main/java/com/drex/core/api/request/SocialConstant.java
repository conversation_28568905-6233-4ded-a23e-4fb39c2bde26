package com.drex.core.api.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.List;

@Data
public class SocialConstant {

    public enum PlatformEnum {
        X,
        Discord,
        YouTube,

        ;
    }

    @AllArgsConstructor
    @Getter
    public enum EventEnum {
        replay(List.of(PlatformEnum.X)),
        watch(List.of(PlatformEnum.YouTube)),
        ;

        private List<PlatformEnum> platform;
    }

    public static final String GLOBAL_APPID = "00000";
    public static final String PREFIX_GLOBAL_TASK_CODE = "global_";

    @Getter
    public enum MaizeLevelEnum {
        NORMAL(5),
        GOLD(10),
        ;

        private long score;

        MaizeLevelEnum(int score) {
            this.score = score;
        }
    }

}
