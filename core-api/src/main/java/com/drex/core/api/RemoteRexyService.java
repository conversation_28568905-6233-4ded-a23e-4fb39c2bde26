package com.drex.core.api;

import com.drex.core.api.request.*;
import com.drex.core.api.response.MaizeDTO;
import com.drex.core.api.response.RexysDTO;
import com.kikitrade.framework.common.model.Response;

import java.util.List;

public interface RemoteRexyService {

    /**
     * 增加恐龙
     */
    Response addRexy(RexyConfigDTO rexyConfigDTO);

    /**
     * 删除恐龙
     */
    Response removeRexy(String rexyId);

    /**
     * 查询恐龙列表
     * @param customerId
     * @return
     */
    Response<List<RexysDTO>> listRexy(String customerId);

    /**
     * 生成玉米接口
     */
    Response<MaizeDTO> generateMaize(GenerateMaizeRequest request);

    /**
     * 查询最新一颗玉米
     * @param request
     * @return
     */
    Response<List<MaizeDTO>> lastMaize(LastMaizeRequest request);

    /**
     * 收集玉米转换成玉米粒到篮子
     */
    Response<MaizeDTO> collectMaize(CollectMaizeRequest request);

    /**
     * 判断玉米能否产生玉米
     * @param contentId
     * @return
     * @throws Exception
     */
    Response<Boolean> checkContentId(String customerId, String contentId, String socialPlatform) throws Exception;
}
