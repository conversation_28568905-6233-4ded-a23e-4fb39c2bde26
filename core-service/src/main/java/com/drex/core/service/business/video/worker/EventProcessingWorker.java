package com.drex.core.service.business.video.worker;

import com.drex.core.service.business.video.PlaybackAnalysisService;
import com.drex.core.service.cache.model.SessionEvent;
import com.drex.core.service.config.YouTubeRewardProperties;
import com.drex.core.service.util.async.callback.ICallback;
import com.drex.core.service.util.async.callback.IWorker;
import com.drex.core.service.util.async.worker.WorkResult;
import com.drex.core.service.util.async.wrapper.WorkerWrapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 事件处理Worker
 * 负责计算有效观看时长和进度
 */
@Slf4j
@Component
public class EventProcessingWorker implements IWorker<EventProcessingWorker.EventProcessingParam, EventProcessingWorker.EventProcessingResult>,
        ICallback<EventProcessingWorker.EventProcessingParam, EventProcessingWorker.EventProcessingResult> {

    @Autowired
    private PlaybackAnalysisService playbackAnalysisService;
    @Autowired
    private YouTubeRewardProperties youTubeRewardProperties;

    @Override
    public EventProcessingResult action(EventProcessingParam param, Map<String, WorkerWrapper> allWrappers) {
        try {
            log.debug("Processing events for session: {}, events count: {}", param.getSessionId(), param.getEvents().size());
            
            // 计算有效观看时长
            int effectiveWatchSeconds = playbackAnalysisService.calculateEffectiveWatchTime(param.getEvents());
            
            // 计算进度
            if (param.getVideoDurationSeconds() <= 0) {
                return null;
            }

            double watchPercentage = (double) effectiveWatchSeconds / param.getVideoDurationSeconds();

            int currentProgress = youTubeRewardProperties.calculateProgress(watchPercentage, param.getVideoUrl());
            log.debug("Event processing completed for session: {}, effectiveWatchSeconds: {}, progress: {}", 
                    param.getSessionId(), effectiveWatchSeconds, currentProgress);
            
            return new EventProcessingResult(effectiveWatchSeconds, watchPercentage, currentProgress);
            
        } catch (Exception e) {
            log.error("Failed to process events for session: {}", param.getSessionId(), e);
            throw new RuntimeException("Event processing failed", e);
        }
    }

    @Override
    public void begin() {
        log.debug("EventProcessingWorker begin");
    }

    @Override
    public void result(boolean success, EventProcessingParam param, WorkResult<EventProcessingResult> result) {
        if (success) {
            log.debug("EventProcessingWorker completed successfully for session: {}", param.getSessionId());
        } else {
            log.error("EventProcessingWorker failed for session: {}", param.getSessionId());
        }
    }

    /**
     * 事件处理参数
     */
    @Data
    public static class EventProcessingParam {
        private final String sessionId;
        private final List<SessionEvent> events;
        private final String videoUrl;
        private final int videoDurationSeconds;

        public EventProcessingParam(String sessionId, List<SessionEvent> events, String videoUrl, int videoDurationSeconds) {
            this.sessionId = sessionId;
            this.events = events;
            this.videoUrl = videoUrl;
            this.videoDurationSeconds = videoDurationSeconds;
        }
    }

    /**
     * 事件处理结果
     */
    @Data
    public static class EventProcessingResult {
        private final int effectiveWatchSeconds;
        private final double watchPercentage;
        private final Integer currentProgress;

        public EventProcessingResult(int effectiveWatchSeconds, double watchPercentage, Integer currentProgress) {
            this.effectiveWatchSeconds = effectiveWatchSeconds;
            this.watchPercentage = watchPercentage;
            this.currentProgress = currentProgress;
        }
    }
}
