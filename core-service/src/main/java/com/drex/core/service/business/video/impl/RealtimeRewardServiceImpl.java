package com.drex.core.service.business.video.impl;

import com.drex.core.api.request.SocialConstant;
import com.drex.core.api.response.VideoReportResponse;
import com.drex.core.dal.tablestore.builder.MaizeRecordBuilder;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import com.drex.core.dal.tablestore.model.VideoViewingSession;
import com.drex.core.service.business.video.RealtimeRewardService;
import com.drex.core.service.business.video.model.FraudIndexResult;
import com.drex.core.service.business.video.worker.EventProcessingWorker;
import com.drex.core.service.business.video.worker.RewardCalculationWorker;
import com.drex.core.service.business.video.worker.RiskScoreCalculationWorker;
import com.drex.core.service.cache.model.SessionEvent;
import com.drex.core.service.util.async.executor.Async;
import com.drex.core.service.util.async.wrapper.WorkerWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实时奖励服务实现
 */
@Slf4j
@Service
public class RealtimeRewardServiceImpl implements RealtimeRewardService {

    @Autowired
    private MaizeRecordBuilder maizeRecordBuilder;

    @Autowired
    private EventProcessingWorker eventProcessingWorker;

    @Autowired
    private RiskScoreCalculationWorker riskScoreCalculationWorker;

    @Autowired
    private RewardCalculationWorker rewardCalculationWorker;

    @Override
    public RealtimeProcessingResult processRealtimeEvents(String customerId, VideoViewingSession session,
                                                          List<SessionEvent> events, SocialConstant.PlatformEnum socialPlatform, SocialConstant.EventEnum socialEvent) {
        String sessionId = session.getSessionId();
        try {
            log.debug("Processing realtime events for session: {}, events: {}", session.getSessionId(), events.size());

            // 使用WorkerWrapper模式编排异步处理流程
            long startTime = System.currentTimeMillis();

            // 创建事件处理Worker
            WorkerWrapper<EventProcessingWorker.EventProcessingParam, EventProcessingWorker.EventProcessingResult> eventWorkerWrapper =
                    new WorkerWrapper.Builder<EventProcessingWorker.EventProcessingParam, EventProcessingWorker.EventProcessingResult>()
                            .worker(eventProcessingWorker)
                            .callback(eventProcessingWorker)
                            .param(new EventProcessingWorker.EventProcessingParam(sessionId, events, session.getVideoUrl(), session.getVideoDurationSeconds()))
                            .build();

            // 创建风险分数计算Worker（并行执行）
            WorkerWrapper<VideoViewingSession, FraudIndexResult> riskScoreWorkerWrapper =
                    new WorkerWrapper.Builder<VideoViewingSession, FraudIndexResult>()
                            .worker(riskScoreCalculationWorker)
                            .callback(riskScoreCalculationWorker)
                            .param(session)
                            .build();

            // 开始执行并行任务
            log.debug("Starting parallel execution for session: {}", sessionId);
            Async.beginWork(3000, eventWorkerWrapper, riskScoreWorkerWrapper); // 3秒超时

            log.debug("Parallel execution completed for session: {}, cost: {}ms",
                    sessionId, System.currentTimeMillis() - startTime);

            // 获取执行结果
            EventProcessingWorker.EventProcessingResult eventResult = eventWorkerWrapper.getWorkResult().getResult();
            FraudIndexResult fraudIndexResult = riskScoreWorkerWrapper.getWorkResult().getResult();
            Double riskScore = fraudIndexResult.getFinalFraudScore();

            if (eventResult == null) {
                log.error("Event processing failed for session: {}", sessionId);
                return new RealtimeProcessingResult(false, "Event processing failed");
            }

            if (riskScore == null) {
                log.warn("Risk score calculation failed for session: {}, using default value", sessionId);
                riskScore = 0.5; // 默认中等风险分数
            }

            RealtimeProcessingResult result = new RealtimeProcessingResult(true, "Processing completed");
            result.setRiskScore(riskScore);

            // 检查是否可以获得奖励
            if (eventResult.getCurrentProgress() != null &&
                canReceiveReward(customerId, sessionId, session.getVideoId(), eventResult.getCurrentProgress())) {

                // 创建奖励计算Worker（依赖前面的结果）
                WorkerWrapper<RewardCalculationWorker.RewardCalculationParam, RewardGenerationResult> rewardWorkerWrapper =
                        new WorkerWrapper.Builder<RewardCalculationWorker.RewardCalculationParam, RewardGenerationResult>()
                                .worker(rewardCalculationWorker)
                                .callback(rewardCalculationWorker)
                                .param(new RewardCalculationWorker.RewardCalculationParam(
                                        sessionId, customerId, session.getVideoId(), session.getVideoUrl(), eventResult.getCurrentProgress(), riskScore, socialPlatform, socialEvent, session.getVideoDurationSeconds(), eventResult.getWatchPercentage()))
                                .build();

                // 执行奖励计算
                log.debug("Starting reward calculation for session: {}, progress: {}", sessionId, eventResult.getCurrentProgress());
                Async.beginWork(2000, rewardWorkerWrapper); // 2秒超时

                RewardGenerationResult rewardResult = rewardWorkerWrapper.getWorkResult().getResult();

                if (rewardResult != null && rewardResult.isSuccess()) {
                    VideoReportResponse.RewardInfo rewardInfo = VideoReportResponse.RewardInfo.builder()
                            .rewardCode(rewardResult.getRewardCode())
                            .rewardAmount(rewardResult.getRewardAmount())
                            .rewardLevel(rewardResult.getRewardLevel())
                            .progress(eventResult.getCurrentProgress())
                            .build();

                    result.setRewardInfo(rewardInfo);
                    result.setHasNewReward(true);
                    log.info("Generated reward for session: {}, progress: {}, code: {}, riskScore: {}",
                            sessionId, eventResult.getCurrentProgress(), rewardResult.getRewardCode(), riskScore);
                } else {
                    log.warn("Reward generation failed for session: {}, progress: {}",
                            sessionId, eventResult.getCurrentProgress());
                }
            }

            log.debug("Realtime processing completed for session: {}, total cost: {}ms",
                    sessionId, System.currentTimeMillis() - startTime);

            result.setEffectiveWatchSeconds(eventResult.getEffectiveWatchSeconds());
            result.setWatchPercentage(eventResult.getWatchPercentage());
            return result;

        } catch (Exception e) {
            log.error("Failed to process realtime events for session: {}", sessionId, e);
            return new RealtimeProcessingResult(false, "Processing failed: " + e.getMessage());
        }
    }



    @Override
    public Integer calculateProgress(int effectiveWatchSeconds, int videoDurationSeconds) {
        if (videoDurationSeconds <= 0) {
            return null;
        }

        double watchPercentage = (double) effectiveWatchSeconds / videoDurationSeconds;
        
        // 根据有效播放时长占视频总时长的百分比计算progress进度
        if (watchPercentage >= 0.2 && watchPercentage <= 0.4) {
            return 1;
        } else if (watchPercentage >= 0.5 && watchPercentage <= 0.7) {
            return 2;
        } else if (watchPercentage >= 0.8 && watchPercentage <= 1.2) {
            return 3;
        }
        
        return null;
    }

    @Override
    public boolean canReceiveReward(String customerId, String sessionId, String videoId, Integer progress) {
        try {
            // 使用组合键查询maizeRecord表检查是否已发放奖励
            // customerId + socialEvent + socialPlatform + socialContentId + sessionId + progress
            MaizeRecord existingRecord = maizeRecordBuilder.findMaizeRecordByCompositeKey(
                    customerId, "watch", videoId, sessionId, progress);
            
            return existingRecord == null;
            
        } catch (Exception e) {
            log.error("Failed to check reward eligibility for session: {}, progress: {}", sessionId, progress, e);
            return false;
        }
    }
}
