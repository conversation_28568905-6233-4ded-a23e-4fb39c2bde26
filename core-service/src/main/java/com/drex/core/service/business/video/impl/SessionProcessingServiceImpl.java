package com.drex.core.service.business.video.impl;

import com.drex.core.api.request.VideoSessionInitRequest;
import com.drex.core.api.response.InformationDTO;
import com.drex.core.api.response.VideoSessionInitResponse;
import com.drex.core.dal.tablestore.builder.VideoViewingSessionBuilder;
import com.drex.core.dal.tablestore.model.VideoViewingSession;
import com.drex.core.model.youtube.YouTubeAntiCheatConstant;
import com.drex.core.service.CoreProperties;
import com.drex.core.service.business.rexy.InformationService;
import com.drex.core.service.business.video.*;
import com.drex.core.service.business.video.model.FraudIndexResult;
import com.drex.core.service.business.video.worker.RiskScoreCalculationWorker;
import com.drex.core.service.cache.SessionEventCacheService;
import com.drex.core.service.cache.model.SessionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 会话处理服务实现
 */
@Slf4j
@Service
public class SessionProcessingServiceImpl implements SessionProcessingService {

    @Autowired
    private VideoViewingSessionBuilder videoViewingSessionBuilder;

    @Autowired
    private SessionEventCacheService sessionEventCacheService;

    @Autowired
    private RiskScoreCalculationWorker riskScoreCalculationWorker;

    @Autowired
    private CoreProperties coreProperties;

    @Autowired
    private InformationService informationService;

    @Override
    public VideoSessionInitResponse initializeSession(VideoSessionInitRequest request) {
        try {
            log.info("Initializing video session for user: {}, video: {}", 
                    request.getCustomerId(), request.getVideoId());

            // 1. 检查用户黑名单
            BlacklistCheckResult userBlacklistResult = checkUserBlacklist(request.getCustomerId());
            if (userBlacklistResult.isBlacklisted()) {
                return VideoSessionInitResponse.builder()
                        .success(false)
                        .failureReason("User is blacklisted: " + userBlacklistResult.getReason())
                        .build();
            }

            // 2. 检查IP黑名单
            BlacklistCheckResult ipBlacklistResult = checkIpBlacklist(request.getClientIp());
            if (ipBlacklistResult.isBlacklisted()) {
                return VideoSessionInitResponse.builder()
                        .success(false)
                        .failureReason("IP is blacklisted: " + ipBlacklistResult.getReason())
                        .build();
            }
            InformationDTO information = informationService.getByLinkId(request.getVideoId());
            // 3. 检查是否为奖励视频
            if (!isRewardVideo(information)) {
                return VideoSessionInitResponse.builder()
                        .success(false)
                        .failureReason("Video is not eligible for rewards")
                        .build();
            }

            // 4. 检查是否已创建session
            VideoViewingSession videoViewingSession = hasUserReceivedReward(request.getCustomerId(), request.getVideoId());
            if (videoViewingSession != null && videoViewingSession.getRewardGranted()) {
                return VideoSessionInitResponse.builder()
                        .success(false)
                        .failureReason("User has already received rewards for this video")
                        .build();
            }

            // 5. 创建会话
            VideoViewingSession session = videoViewingSession != null ? videoViewingSession : createNewSession(request);
            boolean sessionCreated = videoViewingSessionBuilder.insert(session);
            
            if (!sessionCreated) {
                return VideoSessionInitResponse.builder()
                        .success(false)
                        .failureReason("Failed to create session")
                        .build();
            }

            // 6. 生成会话密钥
            String sessionKey = generateSessionKey(session.getSessionId(), request.getCustomerId());
            // 7. 构建响应
            return buildSuccessResponse(session, sessionKey, information);

        } catch (Exception e) {
            log.error("Failed to initialize session for user: {}, video: {}", 
                     request.getCustomerId(), request.getVideoId(), e);
            return VideoSessionInitResponse.builder()
                    .success(false)
                    .failureReason("Internal error occurred")
                    .build();
        }
    }

    @Override
    public SessionUpdateResult updateSession(String sessionId, List<SessionEvent> events) {
        try {
            log.debug("Updating session: {} with {} events", sessionId, events.size());

            // 1. 验证会话存在
            VideoViewingSession session = videoViewingSessionBuilder.getBySessionId(sessionId);
            if (session == null) {
                return new SessionUpdateResult(false, "Session not found", null);
            }

            // 2. 存储事件到缓存
            boolean eventsSaved = sessionEventCacheService.addEvents(sessionId, events);
            if (!eventsSaved) {
                return new SessionUpdateResult(false, "Failed to save events", null);
            }

            // 4. 返回基本确认信息（不包含分析结果）

            return new SessionUpdateResult(true, null, sessionEventCacheService.getEvents(sessionId));

        } catch (Exception e) {
            log.error("Failed to update session: {}", sessionId, e);
            return new SessionUpdateResult(false, "Internal error occurred", null);
        }
    }

    @Override
    public SessionCompletionResult completeSession(String sessionId) {
        try {
            log.info("Completing session: {}", sessionId);

            VideoViewingSession session = videoViewingSessionBuilder.getBySessionId(sessionId);
            if (session == null) {
                return new SessionCompletionResult(false, null);
            }

            // 获取所有事件进行最终分析
            VideoViewingSession viewingSession = videoViewingSessionBuilder.getBySessionId(sessionId);
            FraudIndexResult fraudIndexResult = riskScoreCalculationWorker.execute(viewingSession);

            // 更新会话状态
            session.setSessionStatus(YouTubeAntiCheatConstant.SessionStatus.COMPLETED_PENDING_VALIDATION.getCode());
            session.setFinalRiskScore(fraudIndexResult.getFinalFraudScore());
            session.setCalculatedEngagedSeconds(fraudIndexResult.getEffectiveWatchSeconds());
            session.setServerEndTime(System.currentTimeMillis());

            videoViewingSessionBuilder.update(session);

            SessionCompletionResult result = new SessionCompletionResult(true, session);
            result.setFinalRiskScore(fraudIndexResult.getFinalFraudScore());
            result.setEffectiveWatchSeconds(fraudIndexResult.getEffectiveWatchSeconds());
            result.setFraudIndicators(fraudIndexResult.getIndicators());

            return result;

        } catch (Exception e) {
            log.error("Failed to complete session: {}", sessionId, e);
            return new SessionCompletionResult(false, null);
        }
    }

    @Override
    public SessionStatus getSessionStatus(String sessionId) {
        try {
            VideoViewingSession session = videoViewingSessionBuilder.getBySessionId(sessionId);
            if (session == null) {
                return null;
            }

            SessionStatus status = new SessionStatus(sessionId, session.getSessionStatus(), session.getStartTime());
            status.setLastUpdateTime(session.getModified());
            status.setCurrentRiskScore(session.getFinalRiskScore() != null ? session.getFinalRiskScore() : 0.0);
            status.setActive(!YouTubeAntiCheatConstant.SessionStatus.COMPLETED_PENDING_VALIDATION.getCode()
                    .equals(session.getSessionStatus()));

            // 获取事件统计
            Long eventCount = sessionEventCacheService.getEventCount(sessionId);
            status.setEventCount(eventCount != null ? eventCount.intValue() : 0);

            return status;

        } catch (Exception e) {
            log.error("Failed to get session status: {}", sessionId, e);
            return null;
        }
    }

    @Override
    public boolean validateSession(String sessionId, String customerId) {
        try {
            VideoViewingSession session = videoViewingSessionBuilder.getBySessionId(sessionId);
            return session != null && customerId.equals(session.getCustomerId());
        } catch (Exception e) {
            log.error("Failed to validate session: {}", sessionId, e);
            return false;
        }
    }

    @Override
    public BlacklistCheckResult checkUserBlacklist(String customerId) {
        // TODO: 实现用户黑名单检查逻辑
        // 这里应该查询用户黑名单缓存或数据库
        return new BlacklistCheckResult(false, null);
    }

    @Override
    public BlacklistCheckResult checkIpBlacklist(String ipAddress) {
        // TODO: 实现IP黑名单检查逻辑
        // 这里应该查询IP黑名单缓存或数据库
        return new BlacklistCheckResult(false, null);
    }

    @Override
    public boolean isRewardVideo(InformationDTO information) {
        // TODO: 实现奖励视频检查逻辑
        // 这里应该查询奖励视频配置
        return information != null && information.getIsReward(); // 临时返回true
    }

    @Override
    public VideoViewingSession hasUserReceivedReward(String customerId, String videoId) {
        try {
            return videoViewingSessionBuilder.hasRewardGranted(customerId, videoId);
        } catch (Exception e) {
            log.error("Failed to check user reward status: {}, {}", customerId, videoId, e);
            return null;
        }
    }

    @Override
    public String generateSessionKey(String sessionId, String customerId) {
        // 这里应该使用加密算法生成安全的密钥
        return Base64.getEncoder().encodeToString(sessionId.getBytes());
    }

    @Override
    public int calculateNextReportInterval(int eventCount) {
        // 实现上报间隔计算公式：10+(n/99×10)
        int additionalInterval = (eventCount / YouTubeAntiCheatConstant.INTERVAL_CALCULATION_DIVISOR) 
                * YouTubeAntiCheatConstant.MAX_ADDITIONAL_INTERVAL;
        return YouTubeAntiCheatConstant.BASE_REPORT_INTERVAL + additionalInterval;
    }

    /**
     * 创建新会话
     */
    private VideoViewingSession createNewSession(VideoSessionInitRequest request) {
        VideoViewingSession session = new VideoViewingSession();
        session.setCustomerId(request.getCustomerId());
        session.setVideoId(request.getVideoId());
        session.setVideoUrl(request.getVideoUrl());
        session.setVideoDurationSeconds(request.getVideoDurationSeconds());
        session.setStartTime(request.getClientTimestamp());
        session.setServerStartTime(System.currentTimeMillis());
        session.setSessionStatus(YouTubeAntiCheatConstant.SessionStatus.IN_PROGRESS.getCode());
        session.setExpiredTime(System.currentTimeMillis() +
                coreProperties.getSessionTimeoutMinutes() * 60 * 1000);
        session.setRewardGranted(false);
        return session;
    }

    /**
     * 构建成功响应
     */
    private VideoSessionInitResponse buildSuccessResponse(VideoViewingSession session, String sessionKey, InformationDTO information) {
        return VideoSessionInitResponse.builder()
                .success(true)
                .sessionId(session.getSessionId())
                .reportKey(sessionKey)
                .sessionExpireTime(session.getExpiredTime())
                .rewardStages(buildRewardStages(information))
                .reportConfig(buildReportConfig())
                .build();
    }

    /**
     * 构建奖励阶段信息
     */
    private List<VideoSessionInitResponse.RewardStageInfo> buildRewardStages(InformationDTO information) {
        List<VideoSessionInitResponse.RewardStageInfo> stages = new ArrayList<>();
        
        for (YouTubeAntiCheatConstant.RewardStage stage : YouTubeAntiCheatConstant.RewardStage.values()) {
            VideoSessionInitResponse.RewardStageInfo stageInfo = VideoSessionInitResponse.RewardStageInfo.builder()
                    .stageName(stage.getDescription())
                    .stageCode(stage.getCode())
                    .requiredWatchPercentage(stage.getWatchPercentage())
                    .rewardAmount(information.getRewardAmount())
                    .rewardType("POINT")
                    .claimed(false)
                    .build();
            stages.add(stageInfo);
        }
        
        return stages;
    }

    /**
     * 构建上报配置
     */
    private VideoSessionInitResponse.ReportConfig buildReportConfig() {
        return VideoSessionInitResponse.ReportConfig.builder()
                .baseReportInterval(YouTubeAntiCheatConstant.BASE_REPORT_INTERVAL)
                .maxReportInterval(YouTubeAntiCheatConstant.BASE_REPORT_INTERVAL + 
                        YouTubeAntiCheatConstant.MAX_ADDITIONAL_INTERVAL)
                .intervalCalculationParam(YouTubeAntiCheatConstant.INTERVAL_CALCULATION_DIVISOR)
                .maxEventsPerReport(YouTubeAntiCheatConstant.MAX_REPORT_EVENTS_PER_REQUEST)
                .encryptionEnabled(true)
                .signatureEnabled(true)
                .requiredEventTypes(Arrays.asList("PLAY", "PAUSE", "TIMEUPDATE", "FOCUS", "BLUR",
                        "PLAYING", "PAUSED", "FOCUS_LOST", "FOCUS_GAINED"))
                .optionalEventTypes(Arrays.asList("SEEKING", "SEEK", "VOLUME_CHANGE", "FULLSCREEN_CHANGE", "USER_STATE"))
                .build();
    }

    /**
     * 根据分析结果更新会话
     */
    private void updateSessionWithAnalysisResult(VideoViewingSession session,
                                               FraudIndexResult fraudIndexResult) {
        session.setFinalRiskScore(fraudIndexResult.getFinalFraudScore());
        session.setModified(System.currentTimeMillis());
    }

}
