package com.drex.core.service.business.video.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 欺诈指数结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FraudIndexResult {

    private double rawScore;
    private double normalizedScore;
    private double finalFraudScore;
    private long effectiveWatchSeconds;
    private long videoDurationSeconds;
    private long calculationTime;
    private RiskIndicators indicators;
    private IndicatorThresholds thresholds;
    private IndicatorWeights weights;

    public FraudIndexResult(double rawScore, double normalizedScore, double finalFraudScore) {
        this.rawScore = rawScore;
        this.normalizedScore = normalizedScore;
        this.finalFraudScore = finalFraudScore;
        this.calculationTime = System.currentTimeMillis();
    }
}