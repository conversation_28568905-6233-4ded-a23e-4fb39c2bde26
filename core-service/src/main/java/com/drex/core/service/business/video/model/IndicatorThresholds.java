package com.drex.core.service.business.video.model;

import lombok.Data;

@Data
public class IndicatorThresholds {
    // 重复事件
    private double repeatedEventsThreshold = 5.0;    // 5次/分钟
    private double repeatedEventsMaxAllowed = 10.0;  // 10次/分钟

    // 完成百分比异常
    private double completionExpectedPercentage = 0.9;      // 90%
    private double completionMaxAllowedPercentage = 1.5;    // 150%

    // 低聚焦百分比
    private double focusThreshold = 0.7;             // 70%
    private double focusMinAllowed = 0.3;             // 30%
    // 长时间空闲
    private double idleThreshold = 0.2;              // 20%
    private double idleMaxAllowed = 0.5;              // 50%
    // 环境不一致
    private double envAccountsThreshold = 2.0;        // 2个账号
    private double envAccountsMaxAllowed = 5.0;       // 5个账号
    private double envIpChangesThreshold = 2.0;       // 2次/小时
    private double envIpChangesMaxAllowed = 5.0;      // 5次/小时
    // 时间戳异常
    private double timestampMaxDiffSeconds = 5.0;     // 5秒
    // 异常播放速率
    private double playbackSpeedThreshold = 1.2;      // 1.2x
    private double playbackSpeedMaxAllowed = 1.5;     // 1.5x
    // 异常跳转
    private double seekThreshold = 5.0;               // 5次/分钟
    private double seekMaxAllowed = 10.0;             // 10次/分钟
    // 指纹重复
    private double fingerprintAccountsThreshold = 2.0; // 2个账号
    private double fingerprintAccountsMaxAllowed = 5.0; // 5个账号
}