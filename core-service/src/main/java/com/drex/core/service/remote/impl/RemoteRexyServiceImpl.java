package com.drex.core.service.remote.impl;

import com.drex.core.api.RemoteRexyService;
import com.drex.core.api.common.BusinessMonitorConstant;
import com.drex.core.api.common.CoreException;
import com.drex.core.api.common.CoreResponseCode;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.*;
import com.drex.core.api.response.MaizeDTO;
import com.drex.core.api.response.RexysDTO;
import com.drex.core.dal.tablestore.model.CustomerRexy;
import com.drex.core.service.business.rexy.CustomerRexyService;
import com.drex.core.service.business.rexy.MaizeService;
import com.drex.core.service.business.rexy.RexyConfigService;
import com.drex.core.service.business.rexy.impl.SocialCheckManager;
import com.drex.customer.api.RemotePassportService;
import com.drex.customer.api.response.PassportDTO;
import com.kikitrade.framework.common.model.Response;
import com.kikitrade.framework.observability.metrics.business.KiKiMonitor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@DubboService
public class RemoteRexyServiceImpl implements RemoteRexyService {

    @Resource
    private RexyConfigService rexyConfigService;
    @Resource
    private CustomerRexyService customerRexyService;
    @Resource
    private MaizeService maizeService;
    @Resource
    private KiKiMonitor kiKiMonitor;
    @DubboReference
    private RemotePassportService remotePassportService;
    @Resource
    private SocialCheckManager socialCheckManager;

    @Override
    public Response addRexy(RexyConfigDTO rexyConfigDTO) {
        rexyConfigService.saveRexyConfig(rexyConfigDTO);
        return Response.success(null);
    }

    @Override
    public Response removeRexy(String rexyId) {
        rexyConfigService.delRexyConfig(rexyId);
        return Response.success(null);
    }

    @Override
    public Response<List<RexysDTO>> listRexy(String customerId) {
        try {
            // 获取用户信息
            PassportDTO passportDTO = remotePassportService.getPassportById(customerId);
            String kycLevel = passportDTO.getKycLevel();

            // 获取所有恐龙配置
            List<RexyConfigDTO> rexyConfigs = rexyConfigService.getRexys();
            if (rexyConfigs.isEmpty()) {
                return Response.success(new ArrayList<>());
            }

            // 获取用户拥有的恐龙
            List<CustomerRexy> customerRexys = customerRexyService.getByCustomer(customerId);

            // 当前UTC时间
            long currentUtcTime = java.time.LocalDateTime.now().toEpochSecond(java.time.ZoneOffset.UTC);

            List<RexysDTO> result = new ArrayList<>();
            for (RexyConfigDTO config : rexyConfigs) {
                RexysDTO rexyDTO = new RexysDTO();
                rexyDTO.setId(config.getId());
                rexyDTO.setCode(config.getName());
                rexyDTO.setImage(config.getAvatar());
                rexyDTO.setMiniImage(config.getMiniAvatar());
                rexyDTO.setLevel(config.getLevel());
                rexyDTO.setRate(config.getRate());
                rexyDTO.setLimit(config.getLimit());

                // 默认设置
                rexyDTO.setHas(false);
                rexyDTO.setSelected(false);
                rexyDTO.setStatus(2);//默认open

                // 检查用户KYC等级是否满足要求
                if (Integer.parseInt(kycLevel.substring(1)) != Integer.parseInt(config.getLevel().substring(1))) {
                    rexyDTO.setStatus(1); // lock
                } else {
                    // 检查时间有效性 没生效或者过了有效时间都是coming soon
                    if (config.getEffectiveTime() != null && currentUtcTime < config.getEffectiveTime()) {
                        rexyDTO.setStatus(0); // coming soon
                    } else if (config.getExpirationTime() != null && currentUtcTime > config.getExpirationTime()) {
                        rexyDTO.setStatus(0); // coming soon
                    }
                }

                // 检查用户是否拥有该恐龙
                for (CustomerRexy customerRexy : customerRexys) {
                    if (customerRexy.getRexyId().equals(config.getId())) {
                        rexyDTO.setHas(true);
                        if (RexyConstant.CustomerRexyStatus.ACTIVE.name().equals(customerRexy.getStatus())) {
                            rexyDTO.setSelected(true);
                        }
                        break;
                    }
                }
                result.add(rexyDTO);
            }
            return Response.success(result);
        } catch (Exception e) {
            log.error("listRexy error: {}", e.getMessage(), e);
            return Response.error("9999", "unknown error");
        }
    }

    /**
     * 恐龙产玉米
     * @param request
     * @return
     */
    @Override
    public Response<MaizeDTO> generateMaize(GenerateMaizeRequest request) {
        try  {
            log.info("generateMaize_request:{}", request);
            MaizeDTO maizeDTO = maizeService.generateMaize(request);

            if (Objects.nonNull(maizeDTO)) {
                kiKiMonitor.monitor(BusinessMonitorConstant.GENERATE_MAIZE, new String[]{"code", "success", "platform", request.getSocialPlatform().name()});
                log.info("generateMaize_success:{}", maizeDTO);
            }
            return Response.success(maizeDTO);
          }catch (Exception e){
            kiKiMonitor.monitor(BusinessMonitorConstant.GENERATE_MAIZE, new String[]{"code", "error", "platform", request.getSocialPlatform().name()});
            log.error("generateMaize error:{}", e.getMessage(), e);
            return Response.error("9999","unknown error");
        }
    }

    @Override
    public Response<List<MaizeDTO>> lastMaize(LastMaizeRequest request) {
        return Response.success(maizeService.lastMaize(request));
    }

    /**
     * 恐龙收获玉米
     * @param request
     * @return
     */
    @Override
    public Response<MaizeDTO> collectMaize(CollectMaizeRequest request) {
        try {
            log.info("collectMaize_request:{}", request);
            MaizeDTO maizeDTO = maizeService.collectMaize(request);

            if(maizeDTO != null){
                log.info("collectMaize_success:{}", maizeDTO);
                kiKiMonitor.monitor(BusinessMonitorConstant.COLLECT_MAIZE, new String[]{"code", "success"});
                return Response.success(maizeDTO);
            }
            kiKiMonitor.monitor(BusinessMonitorConstant.COLLECT_MAIZE, new String[]{"code", "maize_not_found"});
            return Response.error(CoreResponseCode.DATA_NOT_FOUND.getCode(), "maize not found");
        } catch (CoreException e) {
            kiKiMonitor.monitor(BusinessMonitorConstant.COLLECT_MAIZE, new String[]{"code", e.getCode().name().toLowerCase()});
            log.error("collectMaize error:{}", e.getMessage(), e);
            return Response.error(e.getCode().getCode(), e.getCode().getKey());
        } catch (Exception e) {
            kiKiMonitor.monitor(BusinessMonitorConstant.COLLECT_MAIZE, new String[]{"code", "exception"});
            log.error("collectMaize error:{}", e.getMessage(), e);
            return Response.error("9999","unknown error");
        }
    }

    @Override
    public Response<Boolean> checkContentId(String customerId, String contentId, String socialPlatform) throws Exception {
        GenerateMaizeRequest request = GenerateMaizeRequest.builder()
                .customerId(customerId)
                .socialPlatform(SocialConstant.PlatformEnum.valueOf(socialPlatform))
                .socialEventBody(SocialEventBody.builder().socialContentId(contentId).build())
                .build();
        boolean check = socialCheckManager.check(request);
        return Response.success(check);
    }
}
