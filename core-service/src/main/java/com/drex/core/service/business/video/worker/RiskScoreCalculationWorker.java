package com.drex.core.service.business.video.worker;

import com.drex.core.dal.tablestore.model.VideoViewingSession;
import com.drex.core.service.business.video.PlaybackAnalysisService;
import com.drex.core.service.business.video.VideoWatchRiskCalculationService;
import com.drex.core.service.business.video.model.FraudIndexResult;
import com.drex.core.service.business.video.model.IndicatorThresholds;
import com.drex.core.service.business.video.model.RiskIndicators;
import com.drex.core.service.cache.SessionEventCacheService;
import com.drex.core.service.cache.model.SessionEvent;
import com.drex.core.service.config.VideoRiskProperties;
import com.drex.core.service.util.async.callback.ICallback;
import com.drex.core.service.util.async.callback.IWorker;
import com.drex.core.service.util.async.worker.WorkResult;
import com.drex.core.service.util.async.wrapper.WorkerWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 风险分数计算Worker
 * 负责计算欺诈风险分数（分值范围 0-100，分数越高风险越高）
 */
@Slf4j
@Component
public class RiskScoreCalculationWorker implements IWorker<VideoViewingSession, FraudIndexResult>,
        ICallback<VideoViewingSession, FraudIndexResult> {

    @Resource
    private VideoRiskProperties videoRiskProperties;
    @Resource
    private SessionEventCacheService sessionEventCacheService;
    @Resource
    private PlaybackAnalysisService playbackAnalysisService;
    @Resource
    private VideoWatchRiskCalculationService videoWatchRiskCalculationService;

    @Override
    public FraudIndexResult action(VideoViewingSession viewingSession, Map<String, WorkerWrapper> allWrappers) {
        return execute(viewingSession);
    }

    public FraudIndexResult execute(VideoViewingSession viewingSession) {
        try {
            log.debug("Calculating risk score for session: {}", viewingSession);
            List<SessionEvent> allEvents = sessionEventCacheService.getEvents(viewingSession.getSessionId());
            int effectiveWatchSeconds = playbackAnalysisService.calculateEffectiveWatchTime(allEvents);
            int videoDurationSeconds = viewingSession.getVideoDurationSeconds();

            // 各欺诈指标的阈值 -> 各欺诈指标的分值
            //todo
            RiskIndicators indicators = calculateBasicIndicators(allEvents, effectiveWatchSeconds, videoRiskProperties.getIndicatorThresholds());

            // 计算欺诈分数
            FraudIndexResult fraudIndexResult = videoWatchRiskCalculationService.calculateFraudScore(indicators);
            fraudIndexResult.setEffectiveWatchSeconds(effectiveWatchSeconds);
            fraudIndexResult.setVideoDurationSeconds(videoDurationSeconds);

            log.debug("Fraud score calculation completed for session: {}, fraudIndexResult: {}", viewingSession.getSessionId(), fraudIndexResult);
            return fraudIndexResult;
        } catch (Exception e) {
            if (viewingSession == null || !viewingSession.getSessionId().startsWith("test-")) {
                log.error("Failed to calculate risk score for session: {}", viewingSession != null ? viewingSession.getSessionId() : "null", e);
            } else {
                log.debug("Test case: Failed to calculate risk score for test session: {}", viewingSession.getSessionId());
            }
            double defaultRiskScore = videoRiskProperties.getDefaultRiskScore();
            return new FraudIndexResult(defaultRiskScore, defaultRiskScore, defaultRiskScore);
        }
    }

    /**
     * 从会话事件中计算基础指标
     * @param events 会话事件列表
     * @param videoDurationSeconds 视频总时长（秒）
     * @return 包含计算指标的RiskIndicators
     */
    private RiskIndicators calculateBasicIndicators(List<SessionEvent> events, int videoDurationSeconds, IndicatorThresholds indicatorThresholds) {
        // 1. 基础统计
        int totalEvents = events.size();
        double focusTime = 0; // 聚焦时间（秒）
        double idleTime = 0;  // 空闲时间（秒）
        int tabSwitchCount = 0; // 标签页切换次数
        double lastEventTime = 0; // 上次事件时间戳
        double lastPlaybackTime = -1; // 上次播放时间
        double totalPlayTime = 0; // 总播放时间
        int playEvents = 0; // 播放事件计数
        int pauseEvents = 0; // 暂停事件计数
        int seekEvents = 0; // 跳转事件计数
        int jumpEvents = 0; // 异常跳转计数事件数
        
        // 按时间戳排序事件
        List<SessionEvent> sortedEvents = events.stream()
                .sorted(Comparator.comparingLong(SessionEvent::getClientTimestamp))
                .collect(Collectors.toList());
        
        // 2. 遍历事件计算基础统计
        for (int i = 0; i < sortedEvents.size(); i++) {
            SessionEvent event = sortedEvents.get(i);
            Map<String, Object> eventData = event.getEventData();
            if (eventData == null) continue;
            
            double currentTime = event.getClientTimestamp() / 1000.0; // 转换为秒
            double timeSinceLastEvent = i > 0 ? (currentTime - lastEventTime) : 0;
            
            // 2.1 统计标签页切换
            if ("FOCUS_CHANGE".equals(event.getEventType())) {
                Boolean tabActive = (Boolean) eventData.get("tabActive");
                if (tabActive != null) {
                    if (tabActive) {
                        // 页面获得焦点，计算空闲时间
                        idleTime += timeSinceLastEvent;
                    } else {
                        // 页面失去焦点，计算聚焦时间
                        focusTime += timeSinceLastEvent;
                        tabSwitchCount++;
                    }
                }
            } 
            // 2.2 统计播放/暂停事件
            else if ("PLAYBACK_STATE_CHANGE".equals(event.getEventType())) {
                String state = (String) eventData.get("newState");
                if ("PLAYING".equals(state)) {
                    playEvents++;
                } else if ("PAUSED".equals(state)) {
                    pauseEvents++;
                }
                
                // 检测播放时间跳跃
                Double playbackTime = eventData.get("currentTime") != null ? 
                        ((Number) eventData.get("currentTime")).doubleValue() : null;
                        
                if (playbackTime != null) {
                    if (lastPlaybackTime > 0) {
                        double timeDiff = Math.abs(playbackTime - lastPlaybackTime - timeSinceLastEvent);
                        if (timeDiff > 10) { // 如果时间跳跃超过10秒，认为是异常跳跃
                            jumpEvents++;
                        }
                    }
                    lastPlaybackTime = playbackTime;
                    totalPlayTime = Math.max(totalPlayTime, playbackTime);
                }
            }
            // 2.3 统计跳转事件
            else if ("SEEKING".equals(event.getEventType())) {
                seekEvents++;
            }
            
            lastEventTime = currentTime;
        }
        
        // 3. 计算会话总时长（秒）
        double sessionDuration = lastEventTime - (sortedEvents.get(0).getClientTimestamp() / 1000.0);
        if (sessionDuration <= 0) {
            sessionDuration = 1; // 避免除以零
        }
        
        // 4. 计算各项指标
        // 4.1 重复事件（每分钟超过阈值）
        double eventsPerMinute = (totalEvents / sessionDuration) * 60;
        double repeatedEventsScore = calculateLinearScore(
                eventsPerMinute, 
                indicatorThresholds.getRepeatedEventsThreshold(),
                indicatorThresholds.getRepeatedEventsMaxAllowed()
        );
        
        // 4.2 完成百分比异常
        double completionPercentage = videoDurationSeconds > 0 ? totalPlayTime / videoDurationSeconds : 0;
        double completionAnomalyScore = calculateLinearScore(
                completionPercentage,
                indicatorThresholds.getCompletionExpectedPercentage(),
                indicatorThresholds.getCompletionMaxAllowedPercentage()
        );
        
        // 4.3 低聚焦百分比
        double focusPercentage = (focusTime + idleTime) > 0 ? focusTime / (focusTime + idleTime) : 0;
        double lowFocusScore = 1 - calculateLinearScore(
                focusPercentage,
                indicatorThresholds.getFocusMinAllowed(),
                indicatorThresholds.getFocusThreshold()
        );
        
        // 4.4 长时间空闲
        double idlePercentage = (focusTime + idleTime) > 0 ? idleTime / (focusTime + idleTime) : 0;
        double longIdleScore = calculateLinearScore(
                idlePercentage,
                indicatorThresholds.getIdleThreshold(),
                indicatorThresholds.getIdleMaxAllowed()
        );
        
        // 4.5 异常播放速率
        double playbackRate = (totalPlayTime / sessionDuration);
        double playbackSpeedScore = calculateLinearScore(
                playbackRate,
                indicatorThresholds.getPlaybackSpeedThreshold(),
                indicatorThresholds.getPlaybackSpeedMaxAllowed()
        );
        
        // 4.6 异常跳转
        double seeksPerMinute = (seekEvents / sessionDuration) * 60;
        double abnormalSeekScore = calculateLinearScore(
                seeksPerMinute,
                indicatorThresholds.getSeekThreshold(),
                indicatorThresholds.getSeekMaxAllowed()
        );
        
        // 4.7 事件顺序异常（简化实现）
        double eventOrderAnomaly = (playEvents > 0 && pauseEvents > 0 && playEvents != pauseEvents) ? 0.5 : 0;
        
        // 5. 构建并返回指标对象
        RiskIndicators indicators = RiskIndicators.builder()
                .repeatedEvents(repeatedEventsScore)
                .abnormalCompletion(completionAnomalyScore)
                .lowFocusDuration(lowFocusScore)
                .longIdleDuration(longIdleScore)
                .excessivePlaybackSpeed(playbackSpeedScore)
                .abnormalSeek(abnormalSeekScore)
                .eventOrderAnomaly(eventOrderAnomaly)
                // 以下指标需要额外信息（如IP、设备指纹等），这里先设为0
                .maliciousIp(0.0)
                .fingerprintDuplication(0.0)
                .environmentInconsistency(0.0)
                .timestampAnomaly(0.0)
                .build();

        log.debug("Calculated indicators: {}", indicators);
        return indicators;
    }

    /**
     * 计算线性分数，用于将指标值映射到0-1范围
     * @param value 当前值
     * @param threshold 阈值，低于此值得分为0
     * @param maxAllowed 最大值，高于此值得分为1
     * @return 0-1之间的分数
     */
    private double calculateLinearScore(double value, double threshold, double maxAllowed) {
        if (value <= threshold) {
            return 0.0;
        } else if (value >= maxAllowed) {
            return 1.0;
        } else {
            return (value - threshold) / (maxAllowed - threshold);
        }
    }

    @Override
    public void begin() {
        log.debug("RiskScoreCalculationWorker begin");
    }

    @Override
    public void result(boolean success, VideoViewingSession viewingSession, WorkResult<FraudIndexResult> result) {
        if (success) {
            log.debug("RiskScoreCalculationWorker completed successfully for session: {}, riskScore: {}",
                    viewingSession.getSessionId(), result);
        } else {
            log.error("RiskScoreCalculationWorker failed for session: {}", viewingSession.getSessionId());
        }
    }

}
