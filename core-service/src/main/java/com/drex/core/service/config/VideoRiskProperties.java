package com.drex.core.service.config;

import com.drex.core.service.business.video.model.IndicatorThresholds;
import com.drex.core.service.business.video.model.IndicatorWeights;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "video.risk")
public class VideoRiskProperties {

    // 系统异常时的默认风险分数
    private double defaultRiskScore = 50;

    // 欺诈指标-权重配置
    @NestedConfigurationProperty
    private IndicatorWeights indicatorWeights;

    // 欺诈指标-阈值配置
    @NestedConfigurationProperty
    private IndicatorThresholds indicatorThresholds;


}
