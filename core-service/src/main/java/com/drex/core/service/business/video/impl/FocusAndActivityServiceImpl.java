//package com.drex.core.service.business.video.impl;
//
//import com.drex.core.model.youtube.YouTubeAntiCheatConstant;
//import com.drex.core.model.youtube.FraudIndicatorThresholds;
//import com.drex.core.service.business.video.FocusAndActivityService;
//import com.drex.core.service.cache.model.SessionEvent;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// * 焦点和活动分析服务实现
// * 基于提供的欺诈指标阈值设计实现
// */
//@Slf4j
//@Service
//public class FocusAndActivityServiceImpl implements FocusAndActivityService {
//
//    // 注意：欺诈指标阈值常量已移至FraudIndicatorThresholds类中统一管理
//
//    @Override
//    public FocusAnalysisResult analyzeFocusTime(List<SessionEvent> events, long totalSessionDuration) {
//        try {
//            List<FocusEvent> focusEvents = extractFocusEvents(events);
//
//            long totalFocusTime = 0;
//            long totalBlurTime = 0;
//            int focusChangeCount = 0;
//
//            // 按时间排序事件
//            List<SessionEvent> sortedEvents = events.stream()
//                    .filter(event -> "FOCUS".equals(event.getEventType()) || "BLUR".equals(event.getEventType()))
//                    .sorted(Comparator.comparing(SessionEvent::getClientTimestamp))
//                    .collect(Collectors.toList());
//
//            boolean currentlyFocused = true; // 假设初始状态为聚焦
//            Long lastStateChangeTime = null;
//
//            if (!sortedEvents.isEmpty()) {
//                lastStateChangeTime = sortedEvents.get(0).getClientTimestamp();
//            }
//
//            for (SessionEvent event : sortedEvents) {
//                long timestamp = event.getClientTimestamp();
//
//                if (lastStateChangeTime != null) {
//                    long duration = timestamp - lastStateChangeTime;
//
//                    if (currentlyFocused) {
//                        totalFocusTime += duration;
//                    } else {
//                        totalBlurTime += duration;
//                    }
//                }
//
//                currentlyFocused = "FOCUS".equals(event.getEventType());
//                lastStateChangeTime = timestamp;
//                focusChangeCount++;
//            }
//
//            // 处理最后一段时间
//            if (lastStateChangeTime != null && !events.isEmpty()) {
//                long lastEventTime = events.stream()
//                        .mapToLong(SessionEvent::getClientTimestamp)
//                        .max()
//                        .orElse(lastStateChangeTime);
//                long remainingDuration = lastEventTime - lastStateChangeTime;
//
//                if (currentlyFocused) {
//                    totalFocusTime += remainingDuration;
//                } else {
//                    totalBlurTime += remainingDuration;
//                }
//            }
//
//            double focusPercentage = totalSessionDuration > 0 ?
//                    (double) totalFocusTime / totalSessionDuration : 1.0;
//
//            FocusAnalysisResult result = new FocusAnalysisResult(totalFocusTime, totalBlurTime, focusPercentage);
//            result.setFocusChangeCount(focusChangeCount);
//
//            // 计算平均聚焦和失焦时长
//            if (focusChangeCount > 0) {
//                result.setAverageFocusDuration((double) totalFocusTime / Math.max(1, focusChangeCount / 2));
//                result.setAverageBlurDuration((double) totalBlurTime / Math.max(1, focusChangeCount / 2));
//            }
//
//            // 检测低聚焦百分比
//            boolean hasLowFocusPercentage = focusPercentage < FraudIndicatorThresholds.LOW_FOCUS_THRESHOLD;
//            result.setHasLowFocusPercentage(hasLowFocusPercentage);
//            result.setFocusEvents(focusEvents);
//
//            return result;
//
//        } catch (Exception e) {
//            log.error("Failed to analyze focus time", e);
//            return new FocusAnalysisResult(totalSessionDuration, 0, 1.0);
//        }
//    }
//
//    @Override
//    public IdleTimeAnalysisResult analyzeIdleTime(List<SessionEvent> events) {
//        try {
//            List<IdleSession> idleSessions = extractIdleSessions(events);
//
//            long totalIdleTime = idleSessions.stream()
//                    .mapToLong(IdleSession::getDuration)
//                    .sum();
//
//            int idleSessionCount = idleSessions.size();
//            long maxContinuousIdleTime = idleSessions.stream()
//                    .mapToLong(IdleSession::getDuration)
//                    .max()
//                    .orElse(0);
//
//            IdleTimeAnalysisResult result = new IdleTimeAnalysisResult(
//                    totalIdleTime, idleSessionCount, maxContinuousIdleTime);
//
//            if (idleSessionCount > 0) {
//                double averageIdleDuration = (double) totalIdleTime / idleSessionCount;
//                result.setAverageIdleDuration(averageIdleDuration);
//            }
//
//            // 计算空闲时间百分比
//            long totalSessionDuration = getSessionDuration(events);
//            double idlePercentage = totalSessionDuration > 0 ?
//                    (double) totalIdleTime / totalSessionDuration : 0.0;
//            result.setIdlePercentage(idlePercentage);
//
//            // 检测长时间空闲
//            boolean hasLongIdleTime = idlePercentage > FraudIndicatorThresholds.LONG_IDLE_THRESHOLD;
//            result.setHasLongIdleTime(hasLongIdleTime);
//            result.setIdleSessions(idleSessions);
//
//            return result;
//
//        } catch (Exception e) {
//            log.error("Failed to analyze idle time", e);
//            return new IdleTimeAnalysisResult(0, 0, 0);
//        }
//    }
//
//    @Override
//    public ActivityStateConsistencyResult analyzeActivityStateConsistency(List<SessionEvent> events) {
//        try {
//            Map<String, Integer> stateTransitions = new HashMap<>();
//            List<String> inconsistencyTypes = new ArrayList<>();
//            int inconsistencyCount = 0;
//
//            // 按时间排序事件
//            List<SessionEvent> sortedEvents = events.stream()
//                    .filter(event -> isActivityStateEvent(event.getEventType()))
//                    .sorted(Comparator.comparing(SessionEvent::getClientTimestamp))
//                    .collect(Collectors.toList());
//
//            String previousState = null;
//            for (SessionEvent event : sortedEvents) {
//                String currentState = extractActivityState(event);
//
//                if (previousState != null && currentState != null) {
//                    String transition = previousState + " -> " + currentState;
//                    stateTransitions.put(transition, stateTransitions.getOrDefault(transition, 0) + 1);
//
//                    // 检测不一致的状态转换
//                    if (isInconsistentTransition(previousState, currentState)) {
//                        inconsistencyTypes.add(transition);
//                        inconsistencyCount++;
//                    }
//                }
//
//                previousState = currentState;
//            }
//
//            // 计算一致性分数
//            double consistencyScore = sortedEvents.size() > 0 ?
//                    1.0 - ((double) inconsistencyCount / sortedEvents.size()) : 1.0;
//
//            boolean isConsistent = inconsistencyCount == 0;
//            ActivityStateConsistencyResult result = new ActivityStateConsistencyResult(
//                    isConsistent, inconsistencyCount, consistencyScore);
//            result.setInconsistencyTypes(inconsistencyTypes);
//            result.setStateTransitions(stateTransitions);
//
//            return result;
//
//        } catch (Exception e) {
//            log.error("Failed to analyze activity state consistency", e);
//            return new ActivityStateConsistencyResult(true, 0, 1.0);
//        }
//    }
//
//    @Override
//    public InteractionBehaviorAnalysisResult analyzeInteractionBehavior(List<SessionEvent> events) {
//        try {
//            Map<String, Integer> interactionTypes = new HashMap<>();
//            int totalInteractions = 0;
//            long lastInteractionTime = 0;
//
//            for (SessionEvent event : events) {
//                if (isInteractionEvent(event.getEventType())) {
//                    interactionTypes.put(event.getEventType(),
//                            interactionTypes.getOrDefault(event.getEventType(), 0) + 1);
//                    totalInteractions++;
//                    lastInteractionTime = Math.max(lastInteractionTime, event.getClientTimestamp());
//                }
//            }
//
//            // 计算交互频率（每分钟）
//            long sessionDurationMinutes = Math.max(1, getSessionDurationMinutes(events));
//            double interactionFrequency = (double) totalInteractions / sessionDurationMinutes;
//
//            InteractionBehaviorAnalysisResult result = new InteractionBehaviorAnalysisResult(
//                    totalInteractions, interactionFrequency);
//            result.setInteractionTypes(interactionTypes);
//            result.setLastInteractionTime(lastInteractionTime);
//
//            // 计算交互分数
//            double interactionScore = Math.min(1.0, interactionFrequency / 10.0); // 假设10次/分钟为满分
//            result.setInteractionScore(interactionScore);
//
//            // 检测异常交互
//            boolean hasAbnormalInteraction = interactionFrequency < 0.5 || interactionFrequency > 50;
//            result.setHasAbnormalInteraction(hasAbnormalInteraction);
//
//            return result;
//
//        } catch (Exception e) {
//            log.error("Failed to analyze interaction behavior", e);
//            return new InteractionBehaviorAnalysisResult(0, 0.0);
//        }
//    }
//
//    @Override
//    public FocusPatternAnomalyResult detectFocusPatternAnomaly(List<SessionEvent> events) {
//        try {
//            List<String> anomalyTypes = new ArrayList<>();
//            double anomalyScore = 0.0;
//
//            // 检测焦点切换频率异常
//            FocusAnalysisResult focusResult = analyzeFocusTime(events, getSessionDuration(events));
//            long sessionDurationMinutes = Math.max(1, getSessionDurationMinutes(events));
//            double focusChangeFrequency = (double) focusResult.getFocusChangeCount() / sessionDurationMinutes;
//
//            if (focusChangeFrequency > 20) { // 超过20次/分钟认为异常
//                anomalyTypes.add("EXCESSIVE_FOCUS_CHANGES");
//                anomalyScore = Math.max(anomalyScore, 0.8);
//            }
//
//            // 检测焦点时间过短
//            if (focusResult.getFocusPercentage() < FraudIndicatorThresholds.LOW_FOCUS_MIN_ALLOWED) {
//                anomalyTypes.add("EXTREMELY_LOW_FOCUS");
//                anomalyScore = Math.max(anomalyScore, 1.0);
//            }
//
//            // 检测焦点模式规律性（可能是脚本）
//            if (detectRegularFocusPattern(events)) {
//                anomalyTypes.add("REGULAR_FOCUS_PATTERN");
//                anomalyScore = Math.max(anomalyScore, 0.9);
//            }
//
//            boolean hasAnomaly = !anomalyTypes.isEmpty();
//            FocusPatternAnomalyResult result = new FocusPatternAnomalyResult(hasAnomaly, anomalyScore);
//            result.setAnomalyTypes(anomalyTypes);
//            result.setPatternDescription(generateFocusPatternDescription(focusResult, anomalyTypes));
//
//            Map<String, Object> details = new HashMap<>();
//            details.put("focusChangeFrequency", focusChangeFrequency);
//            details.put("focusPercentage", focusResult.getFocusPercentage());
//            result.setAnomalyDetails(details);
//
//            return result;
//
//        } catch (Exception e) {
//            log.error("Failed to detect focus pattern anomaly", e);
//            return new FocusPatternAnomalyResult(false, 0.0);
//        }
//    }
//
//    @Override
//    public VisibilityAnalysisResult analyzePageVisibility(List<SessionEvent> events) {
//        try {
//            long totalVisibleTime = 0;
//            long totalHiddenTime = 0;
//            int visibilityChangeCount = 0;
//
//            // 按时间排序可见性事件
//            List<SessionEvent> visibilityEvents = events.stream()
//                    .filter(event -> "VISIBILITY_CHANGE".equals(event.getEventType()))
//                    .sorted(Comparator.comparing(SessionEvent::getClientTimestamp))
//                    .collect(Collectors.toList());
//
//            boolean currentlyVisible = true; // 假设初始状态为可见
//            Long lastChangeTime = null;
//
//            if (!visibilityEvents.isEmpty()) {
//                lastChangeTime = visibilityEvents.get(0).getClientTimestamp();
//            }
//
//            for (SessionEvent event : visibilityEvents) {
//                long timestamp = event.getClientTimestamp();
//
//                if (lastChangeTime != null) {
//                    long duration = timestamp - lastChangeTime;
//
//                    if (currentlyVisible) {
//                        totalVisibleTime += duration;
//                    } else {
//                        totalHiddenTime += duration;
//                    }
//                }
//
//                // 从事件数据中获取可见性状态
//                currentlyVisible = extractVisibilityState(event);
//                lastChangeTime = timestamp;
//                visibilityChangeCount++;
//            }
//
//            // 处理最后一段时间
//            if (lastChangeTime != null && !events.isEmpty()) {
//                long lastEventTime = events.stream()
//                        .mapToLong(SessionEvent::getClientTimestamp)
//                        .max()
//                        .orElse(lastChangeTime);
//                long remainingDuration = lastEventTime - lastChangeTime;
//
//                if (currentlyVisible) {
//                    totalVisibleTime += remainingDuration;
//                } else {
//                    totalHiddenTime += remainingDuration;
//                }
//            }
//
//            double visibilityPercentage = (totalVisibleTime + totalHiddenTime) > 0 ?
//                    (double) totalVisibleTime / (totalVisibleTime + totalHiddenTime) : 1.0;
//
//            VisibilityAnalysisResult result = new VisibilityAnalysisResult(
//                    totalVisibleTime, totalHiddenTime, visibilityPercentage);
//            result.setVisibilityChangeCount(visibilityChangeCount);
//
//            // 检测异常可见性
//            boolean hasAbnormalVisibility = visibilityPercentage < 0.5 || visibilityChangeCount > 100;
//            result.setHasAbnormalVisibility(hasAbnormalVisibility);
//
//            return result;
//
//        } catch (Exception e) {
//            log.error("Failed to analyze page visibility", e);
//            return new VisibilityAnalysisResult(0, 0, 1.0);
//        }
//    }
//
//    @Override
//    public Map<String, Double> calculateFocusActivityFraudIndicators(List<SessionEvent> events,
//                                                                   long totalSessionDuration) {
//        Map<String, Double> indicators = new HashMap<>();
//
//        try {
//            // 2.3 低聚焦百分比 (LOW_FOCUS_DURATION)
//            FocusAnalysisResult focusResult = analyzeFocusTime(events, totalSessionDuration);
//            double lowFocusScore = calculateLowFocusScore(focusResult.getFocusPercentage());
//            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.LOW_FOCUS_PERCENTAGE.getCode(),
//                    lowFocusScore);
//
//            // 2.4 长时间空闲 (LONG_IDLE_DURATION)
//            IdleTimeAnalysisResult idleResult = analyzeIdleTime(events);
//            double longIdleScore = calculateLongIdleScore(idleResult.getIdlePercentage());
//            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.LONG_IDLE_TIME.getCode(),
//                    longIdleScore);
//
//            // 活动状态不一致
//            ActivityStateConsistencyResult consistencyResult = analyzeActivityStateConsistency(events);
//            double inconsistencyScore = 1.0 - consistencyResult.getConsistencyScore();
//            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.ACTIVITY_STATE_INCONSISTENCY.getCode(),
//                    inconsistencyScore);
//
//            // 焦点模式异常
//            FocusPatternAnomalyResult patternResult = detectFocusPatternAnomaly(events);
//            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.FOCUS_PATTERN_ANOMALY.getCode(),
//                    patternResult.getAnomalyScore());
//
//        } catch (Exception e) {
//            log.error("Failed to calculate focus activity fraud indicators", e);
//            // 返回高风险指标
//            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.LOW_FOCUS_PERCENTAGE.getCode(), 1.0);
//        }
//
//        return indicators;
//    }
//
//    /**
//     * 计算低聚焦分数
//     * 使用FraudIndicatorThresholds中的计算公式
//     */
//    private double calculateLowFocusScore(double focusPercentage) {
//        return FraudIndicatorThresholds.calculateLowFocusScore(focusPercentage);
//    }
//
//    /**
//     * 计算长时间空闲分数
//     * 使用FraudIndicatorThresholds中的计算公式
//     */
//    private double calculateLongIdleScore(double idlePercentage) {
//        return FraudIndicatorThresholds.calculateLongIdleScore(idlePercentage);
//    }
//
//    /**
//     * 提取焦点事件
//     */
//    private List<FocusEvent> extractFocusEvents(List<SessionEvent> events) {
//        List<FocusEvent> focusEvents = new ArrayList<>();
//
//        List<SessionEvent> sortedEvents = events.stream()
//                .filter(event -> "FOCUS".equals(event.getEventType()) || "BLUR".equals(event.getEventType()))
//                .sorted(Comparator.comparing(SessionEvent::getClientTimestamp))
//                .collect(Collectors.toList());
//
//        for (int i = 0; i < sortedEvents.size(); i++) {
//            SessionEvent event = sortedEvents.get(i);
//            boolean focused = "FOCUS".equals(event.getEventType());
//            long timestamp = event.getClientTimestamp();
//
//            // 计算持续时间
//            long duration = 0;
//            if (i < sortedEvents.size() - 1) {
//                duration = sortedEvents.get(i + 1).getClientTimestamp() - timestamp;
//            }
//
//            focusEvents.add(new FocusEvent(timestamp, focused, duration));
//        }
//
//        return focusEvents;
//    }
//
//    /**
//     * 提取空闲会话
//     */
//    private List<IdleSession> extractIdleSessions(List<SessionEvent> events) {
//        List<IdleSession> idleSessions = new ArrayList<>();
//
//        // 查找activity_state为IDLE的时间段
//        List<SessionEvent> activityEvents = events.stream()
//                .filter(event -> event.getEventData() != null &&
//                        event.getEventData().containsKey("userActivityState"))
//                .sorted(Comparator.comparing(SessionEvent::getClientTimestamp))
//                .collect(Collectors.toList());
//
//        Long idleStartTime = null;
//        for (SessionEvent event : activityEvents) {
//            String activityState = (String) event.getEventData().get("userActivityState");
//
//            if ("idle".equals(activityState) && idleStartTime == null) {
//                idleStartTime = event.getClientTimestamp();
//            } else if (!"idle".equals(activityState) && idleStartTime != null) {
//                idleSessions.add(new IdleSession(idleStartTime, event.getClientTimestamp()));
//                idleStartTime = null;
//            }
//        }
//
//        return idleSessions;
//    }
//
//    /**
//     * 检查是否为活动状态事件
//     */
//    private boolean isActivityStateEvent(String eventType) {
//        return "FOCUS".equals(eventType) || "BLUR".equals(eventType) ||
//               "VISIBILITY_CHANGE".equals(eventType) || "MOUSE_MOVE".equals(eventType) ||
//               "KEY_PRESS".equals(eventType);
//    }
//
//    /**
//     * 提取活动状态
//     */
//    private String extractActivityState(SessionEvent event) {
//        if (event.getEventData() != null && event.getEventData().containsKey("userActivityState")) {
//            return (String) event.getEventData().get("userActivityState");
//        }
//
//        // 根据事件类型推断状态
//        switch (event.getEventType()) {
//            case "FOCUS": return "active";
//            case "BLUR": return "inactive";
//            case "MOUSE_MOVE":
//            case "KEY_PRESS": return "active";
//            default: return "unknown";
//        }
//    }
//
//    /**
//     * 检查状态转换是否不一致
//     */
//    private boolean isInconsistentTransition(String fromState, String toState) {
//        // 定义不合理的状态转换
//        return false; // 简化实现，可以根据具体需求添加规则
//    }
//
//    /**
//     * 检查是否为交互事件
//     */
//    private boolean isInteractionEvent(String eventType) {
//        return "MOUSE_MOVE".equals(eventType) || "KEY_PRESS".equals(eventType) ||
//               "SCROLL".equals(eventType) || "RESIZE".equals(eventType);
//    }
//
//    /**
//     * 检测规律性焦点模式
//     */
//    private boolean detectRegularFocusPattern(List<SessionEvent> events) {
//        // 简化实现：检测焦点切换是否过于规律
//        List<SessionEvent> focusEvents = events.stream()
//                .filter(event -> "FOCUS".equals(event.getEventType()) || "BLUR".equals(event.getEventType()))
//                .sorted(Comparator.comparing(SessionEvent::getClientTimestamp))
//                .collect(Collectors.toList());
//
//        if (focusEvents.size() < 6) return false;
//
//        // 检测时间间隔是否过于规律
//        List<Long> intervals = new ArrayList<>();
//        for (int i = 1; i < focusEvents.size(); i++) {
//            intervals.add(focusEvents.get(i).getClientTimestamp() -
//                         focusEvents.get(i-1).getClientTimestamp());
//        }
//
//        // 计算间隔的标准差
//        double mean = intervals.stream().mapToLong(Long::longValue).average().orElse(0.0);
//        double variance = intervals.stream()
//                .mapToDouble(interval -> Math.pow(interval - mean, 2))
//                .average()
//                .orElse(0.0);
//        double stdDev = Math.sqrt(variance);
//
//        // 如果标准差很小，说明间隔很规律，可能是脚本
//        return stdDev < mean * 0.1;
//    }
//
//    /**
//     * 生成焦点模式描述
//     */
//    private String generateFocusPatternDescription(FocusAnalysisResult focusResult, List<String> anomalyTypes) {
//        if (anomalyTypes.isEmpty()) {
//            return "Normal focus pattern";
//        } else if (anomalyTypes.contains("EXTREMELY_LOW_FOCUS")) {
//            return "Extremely low focus time detected";
//        } else if (anomalyTypes.contains("EXCESSIVE_FOCUS_CHANGES")) {
//            return "Excessive focus changes detected";
//        } else if (anomalyTypes.contains("REGULAR_FOCUS_PATTERN")) {
//            return "Regular focus pattern suggests automated behavior";
//        } else {
//            return "Anomalous focus pattern detected";
//        }
//    }
//
//    /**
//     * 提取可见性状态
//     */
//    private boolean extractVisibilityState(SessionEvent event) {
//        if (event.getEventData() != null && event.getEventData().containsKey("pageVisible")) {
//            Object visible = event.getEventData().get("pageVisible");
//            return Boolean.TRUE.equals(visible);
//        }
//        return true; // 默认为可见
//    }
//
//    /**
//     * 获取会话持续时间（毫秒）
//     */
//    private long getSessionDuration(List<SessionEvent> events) {
//        if (events.isEmpty()) return 0;
//
//        long startTime = events.stream().mapToLong(SessionEvent::getClientTimestamp).min().orElse(0);
//        long endTime = events.stream().mapToLong(SessionEvent::getClientTimestamp).max().orElse(0);
//
//        return endTime - startTime;
//    }
//
//    /**
//     * 获取会话持续时间（分钟）
//     */
//    private long getSessionDurationMinutes(List<SessionEvent> events) {
//        return Math.max(1, getSessionDuration(events) / (60 * 1000));
//    }
//}
