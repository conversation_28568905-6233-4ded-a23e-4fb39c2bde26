package com.drex.core.service.business.video.impl;

import com.drex.core.service.business.video.VideoWatchRiskCalculationService;
import com.drex.core.service.business.video.model.FraudIndexResult;
import com.drex.core.service.business.video.model.IndicatorWeights;
import com.drex.core.service.business.video.model.RiskIndicators;
import com.drex.core.service.config.VideoRiskProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 欺诈指数计算服务实现
 */
@Slf4j
@Service
public class VideoWatchRiskCalculationServiceImpl implements VideoWatchRiskCalculationService {

    @Resource
    private VideoRiskProperties videoRiskProperties;

    @Override
    public FraudIndexResult calculateFraudScore(RiskIndicators indicators) {
        try {
            log.debug("Calculating fraud score with indicators {}", indicators);

            double rawScore = calculateRawScore(indicators, videoRiskProperties.getIndicatorWeights());
            double normalizedScore = normalizeScore(rawScore);
            double finalFraudScore = applyScoreAdjustments(normalizedScore);

            FraudIndexResult result = new FraudIndexResult(rawScore, normalizedScore, finalFraudScore);
            result.setIndicators(indicators);
            result.setThresholds(videoRiskProperties.getIndicatorThresholds());
            result.setWeights(videoRiskProperties.getIndicatorWeights());

            log.debug("Fraud score calculated: result = {}", result);
            return result;
        } catch (Exception e) {
            log.error("Failed to calculate Fraud score", e);
            return new FraudIndexResult(0.0, 0.0, 0.0);
        }
    }

    /**
     * 计算原始分数
     * 基于提供的欺诈指标阈值设计实现
     * 公式：TrustScore = Σ(IndicatorValue × Weight)
     */
    private double calculateRawScore(RiskIndicators indicators, IndicatorWeights weights) {
        double weightedScore = 0.0;
        double totalWeight = 0.0;

        // 计算每个指标的加权分数
        if (indicators.getRepeatedEvents() > 0) {
            weightedScore += indicators.getRepeatedEvents() * weights.getRepeatedEvents();
            totalWeight += weights.getRepeatedEvents();
        }
        if (indicators.getAbnormalCompletion() > 0) {
            weightedScore += indicators.getAbnormalCompletion() * weights.getAbnormalCompletion();
            totalWeight += weights.getAbnormalCompletion();
        }
        if (indicators.getLowFocusDuration() > 0) {
            weightedScore += indicators.getLowFocusDuration() * weights.getLowFocusDuration();
            totalWeight += weights.getLowFocusDuration();
        }
        if (indicators.getLongIdleDuration() > 0) {
            weightedScore += indicators.getLongIdleDuration() * weights.getLongIdleDuration();
            totalWeight += weights.getLongIdleDuration();
        }
        if (indicators.getEnvironmentInconsistency() > 0) {
            weightedScore += indicators.getEnvironmentInconsistency() * weights.getEnvironmentInconsistency();
            totalWeight += weights.getEnvironmentInconsistency();
        }
        if (indicators.getTimestampAnomaly() > 0) {
            weightedScore += indicators.getTimestampAnomaly() * weights.getTimestampAnomaly();
            totalWeight += weights.getTimestampAnomaly();
        }
        if (indicators.getEventOrderAnomaly() > 0) {
            weightedScore += indicators.getEventOrderAnomaly() * weights.getEventOrderAnomaly();
            totalWeight += weights.getEventOrderAnomaly();
        }
        if (indicators.getExcessivePlaybackSpeed() > 0) {
            weightedScore += indicators.getExcessivePlaybackSpeed() * weights.getExcessivePlaybackSpeed();
            totalWeight += weights.getExcessivePlaybackSpeed();
        }
        if (indicators.getAbnormalSeek() > 0) {
            weightedScore += indicators.getAbnormalSeek() * weights.getAbnormalSeek();
            totalWeight += weights.getAbnormalSeek();
        }
        if (indicators.getFingerprintDuplication() > 0) {
            weightedScore += indicators.getFingerprintDuplication() * weights.getFingerprintDuplication();
            totalWeight += weights.getFingerprintDuplication();
        }
        if (indicators.getMaliciousIp() > 0) {
            weightedScore += indicators.getMaliciousIp() * weights.getMaliciousIp();
            totalWeight += weights.getMaliciousIp();
        }

        // 返回加权平均分数
        return totalWeight > 0 ? weightedScore / totalWeight : 0.0;
    }

    /**
     * 处理特定指标值，应用阈值逻辑
     */
    private double processIndicatorValue(String indicatorType, double rawValue) {
        switch (indicatorType) {
            case "REPEATED_EVENTS":
                // 已在PlaybackAnalysisService中按公式计算
                return rawValue;

            case "ABNORMAL_COMPLETION_PERCENTAGE":
                // 已在PlaybackAnalysisService中按公式计算
                return rawValue;

            case "LOW_FOCUS_DURATION":
                // 已在FocusAndActivityService中按公式计算
                return rawValue;

            case "LONG_IDLE_DURATION":
                // 已在FocusAndActivityService中按公式计算
                return rawValue;

            case "ENVIRONMENT_INCONSISTENCY":
                // 已在EnvironmentAnalysisService中按公式计算
                return rawValue;

            case "TIMESTAMP_ANOMALY":
                // 已在DataValidationService中按公式计算
                return rawValue;

            case "EVENT_ORDER_ANOMALY":
                // 直接触发：若检测到任何顺序异常，IndicatorValue = 1.0
                return rawValue;

            case "EXCESSIVE_PLAYBACK_SPEED":
                // 已在PlaybackAnalysisService中按公式计算
                return rawValue;

            case "ABNORMAL_SEEK":
                // 已在PlaybackAnalysisService中按公式计算
                return rawValue;

            case "FINGERPRINT_DUPLICATION":
                // 已在EnvironmentAnalysisService中按公式计算
                return rawValue;

            case "MALICIOUS_IP":
                // 直接触发：IP在ipReputations表标记为恶意，IndicatorValue = 1.0
                return rawValue;

            default:
                return rawValue;
        }
    }

    /**
     * 标准化分数到0-1范围
     */
    private double normalizeScore(double rawScore) {
        return Math.max(0.0, Math.min(1.0, rawScore));
    }

    /**
     * 应用分数调整
     */
    private double applyScoreAdjustments(double normalizedScore) {
        // 转化为百分制
        return Math.max(0.0, Math.min(1.0, normalizedScore)) * 100;
    }

}
