//package com.drex.core.service.business.video.impl;
//
//import com.alibaba.fastjson2.JSON;
//import com.drex.core.service.business.video.IncrementalCalculationCacheService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.stereotype.Service;
//
//import java.util.HashMap;
//import java.util.Map;
//import java.util.concurrent.TimeUnit;
//
///**
// * 增量计算缓存服务实现
// * 使用Redis存储欺诈指标计算的中间结果
// */
//@Slf4j
//@Service
//public class IncrementalCalculationCacheServiceImpl implements IncrementalCalculationCacheService {
//
//    @Autowired
//    private RedisTemplate<String, String> redisTemplate;
//
//    private static final String CACHE_PREFIX = "youtube:metrics:";
//    private static final String PLAYBACK_SUFFIX = ":playback";
//    private static final String FOCUS_SUFFIX = ":focus";
//    private static final String EVENT_SUFFIX = ":event";
//
//    @Override
//    public void updatePlaybackMetrics(String sessionId, int playTime, int effectivePlayTime,
//                                     int seekCount, double playbackRateSum, int playbackRateCount) {
//        try {
//            String key = CACHE_PREFIX + sessionId + PLAYBACK_SUFFIX;
//            PlaybackMetrics metrics = getPlaybackMetrics(sessionId);
//
//            if (metrics == null) {
//                metrics = new PlaybackMetrics();
//            }
//
//            // 增量更新
//            metrics.setTotalPlayTime(metrics.getTotalPlayTime() + playTime);
//            metrics.setEffectivePlayTime(metrics.getEffectivePlayTime() + effectivePlayTime);
//            metrics.setTotalSeekCount(metrics.getTotalSeekCount() + seekCount);
//            metrics.setPlaybackRateSum(metrics.getPlaybackRateSum() + playbackRateSum);
//            metrics.setPlaybackRateCount(metrics.getPlaybackRateCount() + playbackRateCount);
//
//            // 计算平均播放速率
//            if (metrics.getPlaybackRateCount() > 0) {
//                metrics.setAveragePlaybackRate(metrics.getPlaybackRateSum() / metrics.getPlaybackRateCount());
//            }
//
//            redisTemplate.opsForValue().set(key, JSON.toJSONString(metrics));
//            log.debug("Updated playback metrics for session: {}", sessionId);
//
//        } catch (Exception e) {
//            log.error("Failed to update playback metrics for session: {}", sessionId, e);
//        }
//    }
//
//    @Override
//    public void updateFocusMetrics(String sessionId, long focusTime, long blurTime,
//                                  long idleTime, int interactionCount) {
//        try {
//            String key = CACHE_PREFIX + sessionId + FOCUS_SUFFIX;
//            FocusMetrics metrics = getFocusMetrics(sessionId);
//
//            if (metrics == null) {
//                metrics = new FocusMetrics();
//            }
//
//            // 增量更新
//            metrics.setTotalFocusTime(metrics.getTotalFocusTime() + focusTime);
//            metrics.setTotalBlurTime(metrics.getTotalBlurTime() + blurTime);
//            metrics.setTotalIdleTime(metrics.getTotalIdleTime() + idleTime);
//            metrics.setTotalInteractionCount(metrics.getTotalInteractionCount() + interactionCount);
//
//            // 计算百分比
//            long totalTime = metrics.getTotalFocusTime() + metrics.getTotalBlurTime();
//            if (totalTime > 0) {
//                metrics.setFocusPercentage((double) metrics.getTotalFocusTime() / totalTime);
//            }
//
//            if (totalTime > 0) {
//                metrics.setIdlePercentage((double) metrics.getTotalIdleTime() / totalTime);
//            }
//
//            redisTemplate.opsForValue().set(key, JSON.toJSONString(metrics));
//            log.debug("Updated focus metrics for session: {}", sessionId);
//
//        } catch (Exception e) {
//            log.error("Failed to update focus metrics for session: {}", sessionId, e);
//        }
//    }
//
//    @Override
//    public void updateEventMetrics(String sessionId, Map<String, Integer> eventCounts,
//                                  int duplicateEventCount, int timestampAnomalyCount, int sequenceErrorCount) {
//        try {
//            String key = CACHE_PREFIX + sessionId + EVENT_SUFFIX;
//            EventMetrics metrics = getEventMetrics(sessionId);
//
//            if (metrics == null) {
//                metrics = new EventMetrics();
//                metrics.setEventCounts(new HashMap<>());
//            }
//
//            // 增量更新事件计数
//            Map<String, Integer> currentCounts = metrics.getEventCounts();
//            for (Map.Entry<String, Integer> entry : eventCounts.entrySet()) {
//                currentCounts.put(entry.getKey(),
//                        currentCounts.getOrDefault(entry.getKey(), 0) + entry.getValue());
//            }
//
//            // 增量更新其他指标
//            metrics.setTotalDuplicateEvents(metrics.getTotalDuplicateEvents() + duplicateEventCount);
//            metrics.setTotalTimestampAnomalies(metrics.getTotalTimestampAnomalies() + timestampAnomalyCount);
//            metrics.setTotalSequenceErrors(metrics.getTotalSequenceErrors() + sequenceErrorCount);
//
//            // 计算总事件数
//            int totalEvents = currentCounts.values().stream().mapToInt(Integer::intValue).sum();
//            metrics.setTotalEventCount(totalEvents);
//
//            redisTemplate.opsForValue().set(key, JSON.toJSONString(metrics));
//            log.debug("Updated event metrics for session: {}", sessionId);
//
//        } catch (Exception e) {
//            log.error("Failed to update event metrics for session: {}", sessionId, e);
//        }
//    }
//
//    @Override
//    public PlaybackMetrics getPlaybackMetrics(String sessionId) {
//        try {
//            String key = CACHE_PREFIX + sessionId + PLAYBACK_SUFFIX;
//            String json = redisTemplate.opsForValue().get(key);
//
//            if (json != null) {
//                return JSON.parseObject(json, PlaybackMetrics.class);
//            }
//        } catch (Exception e) {
//            log.error("Failed to get playback metrics for session: {}", sessionId, e);
//        }
//        return null;
//    }
//
//    @Override
//    public FocusMetrics getFocusMetrics(String sessionId) {
//        try {
//            String key = CACHE_PREFIX + sessionId + FOCUS_SUFFIX;
//            String json = redisTemplate.opsForValue().get(key);
//
//            if (json != null) {
//                return JSON.parseObject(json, FocusMetrics.class);
//            }
//        } catch (Exception e) {
//            log.error("Failed to get focus metrics for session: {}", sessionId, e);
//        }
//        return null;
//    }
//
//    @Override
//    public EventMetrics getEventMetrics(String sessionId) {
//        try {
//            String key = CACHE_PREFIX + sessionId + EVENT_SUFFIX;
//            String json = redisTemplate.opsForValue().get(key);
//
//            if (json != null) {
//                return JSON.parseObject(json, EventMetrics.class);
//            }
//        } catch (Exception e) {
//            log.error("Failed to get event metrics for session: {}", sessionId, e);
//        }
//        return null;
//    }
//
//    @Override
//    public AllMetrics getAllMetrics(String sessionId) {
//        try {
//            PlaybackMetrics playbackMetrics = getPlaybackMetrics(sessionId);
//            FocusMetrics focusMetrics = getFocusMetrics(sessionId);
//            EventMetrics eventMetrics = getEventMetrics(sessionId);
//
//            if (playbackMetrics != null || focusMetrics != null || eventMetrics != null) {
//                return new AllMetrics(playbackMetrics, focusMetrics, eventMetrics);
//            }
//        } catch (Exception e) {
//            log.error("Failed to get all metrics for session: {}", sessionId, e);
//        }
//        return null;
//    }
//
//    @Override
//    public void clearMetrics(String sessionId) {
//        try {
//            String playbackKey = CACHE_PREFIX + sessionId + PLAYBACK_SUFFIX;
//            String focusKey = CACHE_PREFIX + sessionId + FOCUS_SUFFIX;
//            String eventKey = CACHE_PREFIX + sessionId + EVENT_SUFFIX;
//
//            redisTemplate.delete(playbackKey);
//            redisTemplate.delete(focusKey);
//            redisTemplate.delete(eventKey);
//
//            log.debug("Cleared metrics for session: {}", sessionId);
//        } catch (Exception e) {
//            log.error("Failed to clear metrics for session: {}", sessionId, e);
//        }
//    }
//
//    @Override
//    public void setExpire(String sessionId, long expireSeconds) {
//        try {
//            String playbackKey = CACHE_PREFIX + sessionId + PLAYBACK_SUFFIX;
//            String focusKey = CACHE_PREFIX + sessionId + FOCUS_SUFFIX;
//            String eventKey = CACHE_PREFIX + sessionId + EVENT_SUFFIX;
//
//            redisTemplate.expire(playbackKey, expireSeconds, TimeUnit.SECONDS);
//            redisTemplate.expire(focusKey, expireSeconds, TimeUnit.SECONDS);
//            redisTemplate.expire(eventKey, expireSeconds, TimeUnit.SECONDS);
//
//            log.debug("Set expire time for session metrics: {} seconds", expireSeconds);
//        } catch (Exception e) {
//            log.error("Failed to set expire time for session: {}", sessionId, e);
//        }
//    }
//}
