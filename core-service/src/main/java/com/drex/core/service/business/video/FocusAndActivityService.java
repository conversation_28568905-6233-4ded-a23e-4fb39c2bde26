//package com.drex.core.service.business.video;
//
//import com.drex.core.service.cache.model.SessionEvent;
//
//import java.util.List;
//import java.util.Map;
//
///**
// * 焦点和活动分析服务接口
// * 负责分析用户焦点状态、活动状态和交互行为
// */
//public interface FocusAndActivityService {
//
//    /**
//     * 计算焦点时间百分比
//     *
//     * @param events 事件列表
//     * @param totalSessionDuration 总会话时长（毫秒）
//     * @return 焦点分析结果
//     */
//    FocusAnalysisResult analyzeFocusTime(List<SessionEvent> events, long totalSessionDuration);
//
//    /**
//     * 检测空闲时间
//     *
//     * @param events 事件列表
//     * @return 空闲时间分析结果
//     */
//    IdleTimeAnalysisResult analyzeIdleTime(List<SessionEvent> events);
//
//    /**
//     * 分析用户活动状态一致性
//     *
//     * @param events 事件列表
//     * @return 状态一致性分析结果
//     */
//    ActivityStateConsistencyResult analyzeActivityStateConsistency(List<SessionEvent> events);
//
//    /**
//     * 分析用户交互行为
//     *
//     * @param events 事件列表
//     * @return 交互行为分析结果
//     */
//    InteractionBehaviorAnalysisResult analyzeInteractionBehavior(List<SessionEvent> events);
//
//    /**
//     * 检测焦点模式异常
//     *
//     * @param events 事件列表
//     * @return 焦点模式异常检测结果
//     */
//    FocusPatternAnomalyResult detectFocusPatternAnomaly(List<SessionEvent> events);
//
//    /**
//     * 分析页面可见性变化
//     *
//     * @param events 事件列表
//     * @return 页面可见性分析结果
//     */
//    VisibilityAnalysisResult analyzePageVisibility(List<SessionEvent> events);
//
//    /**
//     * 计算焦点和活动相关的欺诈指标
//     *
//     * @param events 事件列表
//     * @param totalSessionDuration 总会话时长（毫秒）
//     * @return 欺诈指标映射 (指标类型 -> 指标值)
//     */
//    Map<String, Double> calculateFocusActivityFraudIndicators(List<SessionEvent> events,
//                                                             long totalSessionDuration);
//
//    /**
//     * 焦点分析结果
//     */
//    class FocusAnalysisResult {
//        private long totalFocusTime; // 毫秒
//        private long totalBlurTime; // 毫秒
//        private double focusPercentage;
//        private int focusChangeCount;
//        private double averageFocusDuration;
//        private double averageBlurDuration;
//        private boolean hasLowFocusPercentage;
//        private List<FocusEvent> focusEvents;
//
//        public FocusAnalysisResult(long totalFocusTime, long totalBlurTime, double focusPercentage) {
//            this.totalFocusTime = totalFocusTime;
//            this.totalBlurTime = totalBlurTime;
//            this.focusPercentage = focusPercentage;
//        }
//
//        // Getters and setters
//        public long getTotalFocusTime() { return totalFocusTime; }
//        public long getTotalBlurTime() { return totalBlurTime; }
//        public double getFocusPercentage() { return focusPercentage; }
//        public int getFocusChangeCount() { return focusChangeCount; }
//        public void setFocusChangeCount(int focusChangeCount) { this.focusChangeCount = focusChangeCount; }
//        public double getAverageFocusDuration() { return averageFocusDuration; }
//        public void setAverageFocusDuration(double averageFocusDuration) {
//            this.averageFocusDuration = averageFocusDuration;
//        }
//        public double getAverageBlurDuration() { return averageBlurDuration; }
//        public void setAverageBlurDuration(double averageBlurDuration) {
//            this.averageBlurDuration = averageBlurDuration;
//        }
//        public boolean isHasLowFocusPercentage() { return hasLowFocusPercentage; }
//        public void setHasLowFocusPercentage(boolean hasLowFocusPercentage) {
//            this.hasLowFocusPercentage = hasLowFocusPercentage;
//        }
//        public List<FocusEvent> getFocusEvents() { return focusEvents; }
//        public void setFocusEvents(List<FocusEvent> focusEvents) { this.focusEvents = focusEvents; }
//    }
//
//    /**
//     * 空闲时间分析结果
//     */
//    class IdleTimeAnalysisResult {
//        private long totalIdleTime; // 毫秒
//        private int idleSessionCount;
//        private long maxContinuousIdleTime;
//        private double averageIdleDuration;
//        private boolean hasLongIdleTime;
//        private double idlePercentage;
//        private List<IdleSession> idleSessions;
//
//        public IdleTimeAnalysisResult(long totalIdleTime, int idleSessionCount, long maxContinuousIdleTime) {
//            this.totalIdleTime = totalIdleTime;
//            this.idleSessionCount = idleSessionCount;
//            this.maxContinuousIdleTime = maxContinuousIdleTime;
//        }
//
//        // Getters and setters
//        public long getTotalIdleTime() { return totalIdleTime; }
//        public int getIdleSessionCount() { return idleSessionCount; }
//        public long getMaxContinuousIdleTime() { return maxContinuousIdleTime; }
//        public double getAverageIdleDuration() { return averageIdleDuration; }
//        public void setAverageIdleDuration(double averageIdleDuration) {
//            this.averageIdleDuration = averageIdleDuration;
//        }
//        public boolean isHasLongIdleTime() { return hasLongIdleTime; }
//        public void setHasLongIdleTime(boolean hasLongIdleTime) { this.hasLongIdleTime = hasLongIdleTime; }
//        public double getIdlePercentage() { return idlePercentage; }
//        public void setIdlePercentage(double idlePercentage) { this.idlePercentage = idlePercentage; }
//        public List<IdleSession> getIdleSessions() { return idleSessions; }
//        public void setIdleSessions(List<IdleSession> idleSessions) { this.idleSessions = idleSessions; }
//    }
//
//    /**
//     * 活动状态一致性分析结果
//     */
//    class ActivityStateConsistencyResult {
//        private boolean isConsistent;
//        private int inconsistencyCount;
//        private List<String> inconsistencyTypes;
//        private double consistencyScore;
//        private Map<String, Integer> stateTransitions;
//
//        public ActivityStateConsistencyResult(boolean isConsistent, int inconsistencyCount,
//                                            double consistencyScore) {
//            this.isConsistent = isConsistent;
//            this.inconsistencyCount = inconsistencyCount;
//            this.consistencyScore = consistencyScore;
//        }
//
//        // Getters and setters
//        public boolean isConsistent() { return isConsistent; }
//        public int getInconsistencyCount() { return inconsistencyCount; }
//        public List<String> getInconsistencyTypes() { return inconsistencyTypes; }
//        public void setInconsistencyTypes(List<String> inconsistencyTypes) {
//            this.inconsistencyTypes = inconsistencyTypes;
//        }
//        public double getConsistencyScore() { return consistencyScore; }
//        public Map<String, Integer> getStateTransitions() { return stateTransitions; }
//        public void setStateTransitions(Map<String, Integer> stateTransitions) {
//            this.stateTransitions = stateTransitions;
//        }
//    }
//
//    /**
//     * 交互行为分析结果
//     */
//    class InteractionBehaviorAnalysisResult {
//        private int totalInteractions;
//        private double interactionFrequency; // 每分钟交互次数
//        private Map<String, Integer> interactionTypes;
//        private boolean hasAbnormalInteraction;
//        private double interactionScore;
//        private long lastInteractionTime;
//
//        public InteractionBehaviorAnalysisResult(int totalInteractions, double interactionFrequency) {
//            this.totalInteractions = totalInteractions;
//            this.interactionFrequency = interactionFrequency;
//        }
//
//        // Getters and setters
//        public int getTotalInteractions() { return totalInteractions; }
//        public double getInteractionFrequency() { return interactionFrequency; }
//        public Map<String, Integer> getInteractionTypes() { return interactionTypes; }
//        public void setInteractionTypes(Map<String, Integer> interactionTypes) {
//            this.interactionTypes = interactionTypes;
//        }
//        public boolean isHasAbnormalInteraction() { return hasAbnormalInteraction; }
//        public void setHasAbnormalInteraction(boolean hasAbnormalInteraction) {
//            this.hasAbnormalInteraction = hasAbnormalInteraction;
//        }
//        public double getInteractionScore() { return interactionScore; }
//        public void setInteractionScore(double interactionScore) { this.interactionScore = interactionScore; }
//        public long getLastInteractionTime() { return lastInteractionTime; }
//        public void setLastInteractionTime(long lastInteractionTime) {
//            this.lastInteractionTime = lastInteractionTime;
//        }
//    }
//
//    /**
//     * 焦点模式异常检测结果
//     */
//    class FocusPatternAnomalyResult {
//        private boolean hasAnomaly;
//        private List<String> anomalyTypes;
//        private double anomalyScore;
//        private String patternDescription;
//        private Map<String, Object> anomalyDetails;
//
//        public FocusPatternAnomalyResult(boolean hasAnomaly, double anomalyScore) {
//            this.hasAnomaly = hasAnomaly;
//            this.anomalyScore = anomalyScore;
//        }
//
//        // Getters and setters
//        public boolean isHasAnomaly() { return hasAnomaly; }
//        public List<String> getAnomalyTypes() { return anomalyTypes; }
//        public void setAnomalyTypes(List<String> anomalyTypes) { this.anomalyTypes = anomalyTypes; }
//        public double getAnomalyScore() { return anomalyScore; }
//        public String getPatternDescription() { return patternDescription; }
//        public void setPatternDescription(String patternDescription) {
//            this.patternDescription = patternDescription;
//        }
//        public Map<String, Object> getAnomalyDetails() { return anomalyDetails; }
//        public void setAnomalyDetails(Map<String, Object> anomalyDetails) {
//            this.anomalyDetails = anomalyDetails;
//        }
//    }
//
//    /**
//     * 页面可见性分析结果
//     */
//    class VisibilityAnalysisResult {
//        private long totalVisibleTime;
//        private long totalHiddenTime;
//        private double visibilityPercentage;
//        private int visibilityChangeCount;
//        private boolean hasAbnormalVisibility;
//
//        public VisibilityAnalysisResult(long totalVisibleTime, long totalHiddenTime,
//                                      double visibilityPercentage) {
//            this.totalVisibleTime = totalVisibleTime;
//            this.totalHiddenTime = totalHiddenTime;
//            this.visibilityPercentage = visibilityPercentage;
//        }
//
//        // Getters and setters
//        public long getTotalVisibleTime() { return totalVisibleTime; }
//        public long getTotalHiddenTime() { return totalHiddenTime; }
//        public double getVisibilityPercentage() { return visibilityPercentage; }
//        public int getVisibilityChangeCount() { return visibilityChangeCount; }
//        public void setVisibilityChangeCount(int visibilityChangeCount) {
//            this.visibilityChangeCount = visibilityChangeCount;
//        }
//        public boolean isHasAbnormalVisibility() { return hasAbnormalVisibility; }
//        public void setHasAbnormalVisibility(boolean hasAbnormalVisibility) {
//            this.hasAbnormalVisibility = hasAbnormalVisibility;
//        }
//    }
//
//    /**
//     * 焦点事件
//     */
//    class FocusEvent {
//        private long timestamp;
//        private boolean focused;
//        private long duration;
//
//        public FocusEvent(long timestamp, boolean focused, long duration) {
//            this.timestamp = timestamp;
//            this.focused = focused;
//            this.duration = duration;
//        }
//
//        public long getTimestamp() { return timestamp; }
//        public boolean isFocused() { return focused; }
//        public long getDuration() { return duration; }
//    }
//
//    /**
//     * 空闲会话
//     */
//    class IdleSession {
//        private long startTime;
//        private long endTime;
//        private long duration;
//
//        public IdleSession(long startTime, long endTime) {
//            this.startTime = startTime;
//            this.endTime = endTime;
//            this.duration = endTime - startTime;
//        }
//
//        public long getStartTime() { return startTime; }
//        public long getEndTime() { return endTime; }
//        public long getDuration() { return duration; }
//    }
//}
