package com.drex.core.service.business.video.model;

import lombok.Data;

@Data
public class IndicatorWeights {
    private double repeatedEvents = 0.05;
    private double abnormalCompletion = 0.05;
    private double lowFocusDuration = 0.2;
    private double longIdleDuration = 0.05;
    private double environmentInconsistency = 0.05;
    private double timestampAnomaly = 0.05;
    private double eventOrderAnomaly = 0.025;
    private double excessivePlaybackSpeed = 0.3;
    private double abnormalSeek = 0.025;
    private double fingerprintDuplication = 0.1;
    private double maliciousIp = 0.15;
}