package com.drex.core.service.business.video;

import com.drex.core.service.business.video.model.FraudIndexResult;
import com.drex.core.service.business.video.model.RiskIndicators;

/**
 * 观看视频欺诈指数计算服务
 */
public interface VideoWatchRiskCalculationService {

    /**
     * 计算综合观看视频欺诈指数
     * 基础公式框架：TrustScore = Σ(IndicatorValue × Weight)
     *
     * @param indicators 所有欺诈指标 (指标类型 -> 指标值)
     * @return 欺诈指数计算结果
     */
    FraudIndexResult calculateFraudScore(RiskIndicators indicators);

}
