package com.drex.core.service.business.video;

import com.drex.core.api.request.VideoSessionInitRequest;
import com.drex.core.api.response.InformationDTO;
import com.drex.core.api.response.VideoSessionInitResponse;
import com.drex.core.dal.tablestore.model.VideoViewingSession;
import com.drex.core.service.business.video.model.RiskIndicators;
import com.drex.core.service.cache.model.SessionEvent;
import lombok.Data;

import java.util.List;

/**
 * 会话处理服务接口
 * 负责管理视频观看会话的生命周期，协调各种分析服务
 */
public interface SessionProcessingService {

    /**
     * 初始化视频观看会话
     *
     * @param request 会话初始化请求
     * @return 会话初始化响应
     */
    VideoSessionInitResponse initializeSession(VideoSessionInitRequest request);

    /**
     * 更新会话状态
     *
     * @param sessionId 会话ID
     * @param events 新的事件列表
     * @return 更新结果
     */
    SessionUpdateResult updateSession(String sessionId, List<SessionEvent> events);

    /**
     * 完成会话并进行最终分析
     *
     * @param sessionId 会话ID
     * @return 会话完成结果
     */
    SessionCompletionResult completeSession(String sessionId);

    /**
     * 获取会话当前状态
     *
     * @param sessionId 会话ID
     * @return 会话状态
     */
    SessionStatus getSessionStatus(String sessionId);

    /**
     * 验证会话是否有效
     *
     * @param sessionId 会话ID
     * @param customerId 用户ID
     * @return 是否有效
     */
    boolean validateSession(String sessionId, String customerId);

    /**
     * 检查用户是否在黑名单中
     *
     * @param customerId 用户ID
     * @return 黑名单检查结果
     */
    BlacklistCheckResult checkUserBlacklist(String customerId);

    /**
     * 检查IP是否在黑名单中
     *
     * @param ipAddress IP地址
     * @return 黑名单检查结果
     */
    BlacklistCheckResult checkIpBlacklist(String ipAddress);

    /**
     * 检查视频是否为奖励视频
     *
     * @param information 视频ID
     * @return 是否为奖励视频
     */
    boolean isRewardVideo(InformationDTO information);

    /**
     * 检查用户是否已获得该视频的奖励
     *
     * @param customerId 用户ID
     * @param videoId 视频ID
     * @return 是否已获得奖励
     */
    VideoViewingSession hasUserReceivedReward(String customerId, String videoId);

    /**
     * 生成会话密钥
     *
     * @param sessionId 会话ID
     * @param customerId 用户ID
     * @return 密钥
     */
    String generateSessionKey(String sessionId, String customerId);

    /**
     * 计算下次上报间隔
     *
     * @param eventCount 当前事件数量
     * @return 上报间隔（秒）
     */
    int calculateNextReportInterval(int eventCount);

    /**
     * 会话更新结果
     */
    @Data
    class SessionUpdateResult {
        private boolean success;
        private String errorMessage;
        private List<SessionEvent> allEvents;


        public SessionUpdateResult(boolean success, String errorMessage, List<SessionEvent> allEvents) {
            this.success = success;
            this.errorMessage = errorMessage;
            this.allEvents = allEvents;
        }

    }

    /**
     * 会话完成结果
     */
    class SessionCompletionResult {
        private boolean success;
        private VideoViewingSession finalSession;
        private double finalRiskScore;
        private long totalWatchSeconds;
        private long effectiveWatchSeconds;
        private RiskIndicators riskIndicators;
        private String completionReason;

        public SessionCompletionResult(boolean success, VideoViewingSession finalSession) {
            this.success = success;
            this.finalSession = finalSession;
        }

        public boolean isSuccess() { return success; }
        public VideoViewingSession getFinalSession() { return finalSession; }
        public double getFinalRiskScore() { return finalRiskScore; }
        public void setFinalRiskScore(double finalTrustScore) { this.finalRiskScore = finalTrustScore; }
        public long getTotalWatchSeconds() { return totalWatchSeconds; }
        public void setTotalWatchSeconds(int totalWatchSeconds) { this.totalWatchSeconds = totalWatchSeconds; }
        public long getEffectiveWatchSeconds() { return effectiveWatchSeconds; }
        public void setEffectiveWatchSeconds(long effectiveWatchSeconds) {
            this.effectiveWatchSeconds = effectiveWatchSeconds; 
        }
        public RiskIndicators getFraudIndicators() { return riskIndicators; }
        public void setFraudIndicators(RiskIndicators riskIndicators) {
            this.riskIndicators = riskIndicators;
        }
        public String getCompletionReason() { return completionReason; }
        public void setCompletionReason(String completionReason) { this.completionReason = completionReason; }
    }

    /**
     * 会话状态
     */
    class SessionStatus {
        private String sessionId;
        private String status;
        private long startTime;
        private long lastUpdateTime;
        private int eventCount;
        private int watchSeconds;
        private double currentRiskScore;
        private boolean isActive;

        public SessionStatus(String sessionId, String status, long startTime) {
            this.sessionId = sessionId;
            this.status = status;
            this.startTime = startTime;
        }

        // Getters and setters
        public String getSessionId() { return sessionId; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public long getStartTime() { return startTime; }
        public long getLastUpdateTime() { return lastUpdateTime; }
        public void setLastUpdateTime(long lastUpdateTime) { this.lastUpdateTime = lastUpdateTime; }
        public int getEventCount() { return eventCount; }
        public void setEventCount(int eventCount) { this.eventCount = eventCount; }
        public int getWatchSeconds() { return watchSeconds; }
        public void setWatchSeconds(int watchSeconds) { this.watchSeconds = watchSeconds; }
        public double getCurrentRiskScore() { return currentRiskScore; }
        public void setCurrentRiskScore(double currentRiskScore) { this.currentRiskScore = currentRiskScore; }
        public boolean isActive() { return isActive; }
        public void setActive(boolean active) { isActive = active; }
    }

    /**
     * 黑名单检查结果
     */
    class BlacklistCheckResult {
        private boolean isBlacklisted;
        private String reason;
        private long blacklistTime;
        private String riskLevel;

        public BlacklistCheckResult(boolean isBlacklisted, String reason) {
            this.isBlacklisted = isBlacklisted;
            this.reason = reason;
        }

        public boolean isBlacklisted() { return isBlacklisted; }
        public String getReason() { return reason; }
        public long getBlacklistTime() { return blacklistTime; }
        public void setBlacklistTime(long blacklistTime) { this.blacklistTime = blacklistTime; }
        public String getRiskLevel() { return riskLevel; }
        public void setRiskLevel(String riskLevel) { this.riskLevel = riskLevel; }
    }

}
