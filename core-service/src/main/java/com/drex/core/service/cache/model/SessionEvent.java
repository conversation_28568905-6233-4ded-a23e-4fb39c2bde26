package com.drex.core.service.cache.model;

import com.drex.core.api.request.SocialConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 会话事件模型
 * 对应设计方案中的sessionEvents缓存结构
 * 与VideoReportRequest.EventData结构匹配
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionEvent implements Serializable {

    /**
     * 事件唯一标识符（对应VideoReportRequest.EventData.eventId）
     */
    private String id;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private String customerId;

    /**
     * 客户端时间戳（对应VideoReportRequest.EventData.timestamp）
     */
    private Long clientTimestamp;

    /**
     * 服务器时间戳
     */
    private Long serverTimestamp;

    /**
     * 事件类型（对应VideoReportRequest.EventData.eventType）
     * 支持的类型：PLAYING、PAUSED、SEEK、FOCUS_LOST、FOCUS_GAINED、USER_STATE
     */
    private String eventType;

    /**
     * 事件序号（对应VideoReportRequest.EventData.sequence）
     */
    private Integer sequence;

    /**
     * 社交平台（对应VideoReportRequest.socialPlatform）
     */
    private SocialConstant.PlatformEnum socialPlatform;

    /**
     * 社交事件（对应VideoReportRequest.socialEvent）
     */
    private SocialConstant.EventEnum socialEvent;

    /**
     * 设备指纹（对应VideoReportRequest.deviceFinger）
     */
    private String deviceFinger;

    /**
     * 事件来源IP
     */
    private String sourceIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 创建时间
     */
    private Long created;

    /**
     * 事件详细数据（对应VideoReportRequest.EventData.details）
     * 根据eventType的不同，包含不同的数据结构
     */
    private EventDetails details;

    /**
     * 兼容性字段：原有的事件数据Map格式
     * 用于向后兼容，逐步迁移到details字段
     * @deprecated 建议使用details字段
     */
    @Deprecated
    private Map<String, Object> eventData;

    /**
     * 事件详细数据
     * 根据eventType的不同，包含不同的数据结构
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EventDetails implements Serializable {
        /**
         * PLAYING状态事件数据
         */
        private PlayingEventData playingData;

        /**
         * PAUSED状态事件数据
         */
        private PausedEventData pausedData;

        /**
         * SEEK跳转事件数据
         */
        private SeekEventData seekData;

        /**
         * FOCUS_LOST失焦事件数据
         */
        private FocusLostEventData focusLostData;

        /**
         * FOCUS_GAINED聚焦事件数据
         */
        private FocusGainedEventData focusGainedData;

        /**
         * USER_STATE用户状态事件数据
         */
        private UserStateEventData userStateData;
    }

    /**
     * PLAYING状态事件数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PlayingEventData implements Serializable {
        /**
         * 新状态
         */
        private String newState;

        /**
         * 当前播放时间（秒）
         */
        private Double currentTime;

        /**
         * 播放速率
         */
        private Double playbackRate;
    }

    /**
     * PAUSED状态事件数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PausedEventData implements Serializable {
        /**
         * 新状态
         */
        private String newState;

        /**
         * 当前播放时间（秒）
         */
        private Double currentTime;
    }

    /**
     * SEEK跳转事件数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SeekEventData implements Serializable {
        /**
         * 当前播放时间（秒）
         */
        private Double currentTime;

        /**
         * 之前播放时间（秒）
         */
        private Double previousTime;
    }

    /**
     * FOCUS_LOST失焦事件数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FocusLostEventData implements Serializable {
        /**
         * 标签页是否活跃
         */
        private Boolean tabActive;

        /**
         * 窗口是否聚焦
         */
        private Boolean windowFocused;
    }

    /**
     * FOCUS_GAINED聚焦事件数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FocusGainedEventData implements Serializable {
        /**
         * 标签页是否活跃
         */
        private Boolean tabActive;

        /**
         * 窗口是否聚焦
         */
        private Boolean windowFocused;
    }

    /**
     * USER_STATE用户状态事件数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserStateEventData implements Serializable {
        /**
         * 用户状态：ACTIVE/IDLE/LOCKED
         */
        private String state;
    }
}