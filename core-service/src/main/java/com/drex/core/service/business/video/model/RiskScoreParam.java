//package com.drex.core.service.business.video.model;
//
//import com.drex.core.service.cache.model.SessionEvent;
//import lombok.Builder;
//import lombok.Data;
//
//import java.util.List;
//
///**
// * 风险分数计算参数
// */
//@Data
//@Builder
//public class RiskScoreParam {
//    private final String sessionId;
//    private final List<SessionEvent> events;
//    private final int videoDurationSeconds;
//
//    public RiskScoreParam(String sessionId, List<SessionEvent> events, int videoDurationSeconds) {
//        this.sessionId = sessionId;
//        this.events = events;
//        this.videoDurationSeconds = videoDurationSeconds;
//    }
//
//}