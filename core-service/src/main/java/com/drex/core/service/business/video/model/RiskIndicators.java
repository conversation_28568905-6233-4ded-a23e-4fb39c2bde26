package com.drex.core.service.business.video.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 风险指标计算结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskIndicators {
    /** 重复事件指标 */
    private double repeatedEvents;
    
    /** 异常完成度指标 */
    private double abnormalCompletion;
    
    /** 低聚焦时长指标 */
    private double lowFocusDuration;
    
    /** 长时间空闲指标 */
    private double longIdleDuration;
    
    /** 环境不一致性指标 */
    private double environmentInconsistency;
    
    /** 时间戳异常指标 */
    private double timestampAnomaly;
    
    /** 事件顺序异常指标 */
    private double eventOrderAnomaly;
    
    /** 异常播放速度指标 */
    private double excessivePlaybackSpeed;
    
    /** 异常跳转指标 */
    private double abnormalSeek;
    
    /** 指纹重复指标 */
    private double fingerprintDuplication;
    
    /** 恶意IP指标 */
    private double maliciousIp;
    
}
