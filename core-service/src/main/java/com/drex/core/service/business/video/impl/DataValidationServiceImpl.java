package com.drex.core.service.business.video.impl;

import com.alibaba.fastjson2.JSON;
import com.drex.core.api.request.VideoReportRequest;
import com.drex.core.model.youtube.YouTubeAntiCheatConstant;
import com.drex.core.service.business.video.DataValidationService;
import com.drex.core.service.cache.model.SessionEvent;
import com.drex.core.service.util.EventDataConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据验证服务实现
 */
@Slf4j
@Service
public class DataValidationServiceImpl implements DataValidationService {

    private static final long MAX_TIME_DIFFERENCE_MS = 5000; // 5秒

    @Autowired
    private EventDataConverter eventDataConverter;

    @Override
    public ValidationResult validateReportRequest(VideoReportRequest request) {
        try {
            // 1. 基础字段验证
            if (request.getSessionId() == null || request.getSessionId().trim().isEmpty()) {
                return new ValidationResult(false, "Session ID is required", "MISSING_SESSION_ID");
            }

            if (request.getCustomerId() == null || request.getCustomerId().trim().isEmpty()) {
                return new ValidationResult(false, "Customer ID is required", "MISSING_CUSTOMER_ID");
            }

//            if (request.getSignature() == null || request.getSignature().trim().isEmpty()) {
//                return new ValidationResult(false, "Signature is required", "MISSING_SIGNATURE");
//            }

            // 2. 时间戳验证
            if (request.getClientTimestamp() == null) {
                return new ValidationResult(false, "Client timestamp is required", "MISSING_TIMESTAMP");
            }

            // 3. 设备指纹验证
            if (request.getDeviceFinger() == null || request.getDeviceFinger().trim().isEmpty()) {
                return new ValidationResult(false, "Device finger is required", "MISSING_DEVICE_FINGER");
            }

            // 4. 事件列表验证（如果已解密）
            if (request.getEvents() != null && !request.getEvents().isEmpty()) {
                for (VideoReportRequest.EventData event : request.getEvents()) {
                    if (!isValidEventType(event.getEventType())) {
                        return new ValidationResult(false,
                                "Invalid event type: " + event.getEventType(),
                                "INVALID_EVENT_TYPE");
                    }

                    // 验证新的事件详细数据结构
                    ValidationResult eventDetailsResult = validateEventDetails(event);
                    if (!eventDetailsResult.isValid()) {
                        return eventDetailsResult;
                    }
                }
            }

            return new ValidationResult(true, "Validation passed", "SUCCESS");

        } catch (Exception e) {
            log.error("Failed to validate report request", e);
            return new ValidationResult(false, "Validation error occurred", "VALIDATION_ERROR");
        }
    }

    @Override
    public List<SessionEvent> validateAndDecryptEvents(VideoReportRequest request, String signature, String secretKey) {
        try {
            // 1. 验证签名
//            if (!verifySignature(encryptedData, signature, secretKey)) {
//                log.warn("Invalid signature detected");
//                return new ArrayList<>();
//            }

            // 2. 使用EventDataConverter转换事件数据
            List<SessionEvent> events = eventDataConverter.convertToSessionEvents(
                    request.getEvents(),
                    request.getSessionId(),
                    request.getCustomerId(),
                    request.getSocialPlatform(),
                    request.getSocialEvent(),
                    request.getDeviceFinger(),
                    request.getSourceIp()
            );

            // 3. 验证事件格式
            return events.stream()
                    .filter(event -> validateEventFormat(event).isValid())
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Failed to validate and decrypt events", e);
            return new ArrayList<>();
        }
    }

    @Override
    public TimestampValidationResult validateTimestamps(List<SessionEvent> events, Long serverTimestamp) {
        List<String> anomalousEventIds = new ArrayList<>();
        double maxTimeDifference = 0.0;
        int anomalousCount = 0;

        for (SessionEvent event : events) {
            if (event.getClientTimestamp() != null && event.getServerTimestamp() != null) {
                double timeDiff = Math.abs(event.getServerTimestamp() - event.getClientTimestamp());
                maxTimeDifference = Math.max(maxTimeDifference, timeDiff);

                if (timeDiff > MAX_TIME_DIFFERENCE_MS) {
                    anomalousEventIds.add(event.getId());
                    anomalousCount++;
                }
            }
        }

        boolean isValid = anomalousCount == 0;
        return new TimestampValidationResult(isValid, maxTimeDifference, anomalousCount, anomalousEventIds);
    }

    @Override
    public EventSequenceValidationResult validateEventSequence(List<SessionEvent> events) {
        List<String> invalidTransitions = new ArrayList<>();
        int sequenceErrors = 0;

        // 按时间戳排序事件
        List<SessionEvent> sortedEvents = events.stream()
                .sorted(Comparator.comparing(SessionEvent::getClientTimestamp))
                .collect(Collectors.toList());

        String previousEventType = null;
        for (SessionEvent event : sortedEvents) {
            String currentEventType = event.getEventType();
            
            if (previousEventType != null) {
                if (!isValidTransition(previousEventType, currentEventType)) {
                    invalidTransitions.add(previousEventType + " -> " + currentEventType);
                    sequenceErrors++;
                }
            }
            
            previousEventType = currentEventType;
        }

        boolean isValid = sequenceErrors == 0;
        return new EventSequenceValidationResult(isValid, sequenceErrors, invalidTransitions);
    }

    @Override
    public DuplicateEventDetectionResult detectDuplicateEvents(List<SessionEvent> events) {
        Map<String, Integer> eventSignatures = new HashMap<>();
        List<String> duplicateEventIds = new ArrayList<>();
        int duplicateCount = 0;

        for (SessionEvent event : events) {
            String signature = generateEventSignature(event);
            int count = eventSignatures.getOrDefault(signature, 0) + 1;
            eventSignatures.put(signature, count);

            if (count > 1) {
                duplicateEventIds.add(event.getId());
                duplicateCount++;
            }
        }

        boolean hasDuplicates = duplicateCount > 0;
        DuplicateEventDetectionResult result = new DuplicateEventDetectionResult(
                hasDuplicates, duplicateCount, duplicateEventIds);
        result.setDuplicateGroups(eventSignatures.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));

        return result;
    }

    @Override
    public FormatValidationResult validateEventFormat(SessionEvent event) {
        List<String> formatErrors = new ArrayList<>();

        // 验证必填字段
        if (event.getId() == null || event.getId().trim().isEmpty()) {
            formatErrors.add("Event ID is required");
        }

        if (event.getEventType() == null || event.getEventType().trim().isEmpty()) {
            formatErrors.add("Event type is required");
        } else if (!isValidEventType(event.getEventType())) {
            formatErrors.add("Invalid event type: " + event.getEventType());
        }

        if (event.getClientTimestamp() == null) {
            formatErrors.add("Client timestamp is required");
        }

        if (event.getSessionId() == null || event.getSessionId().trim().isEmpty()) {
            formatErrors.add("Session ID is required");
        }

        if (event.getCustomerId() == null || event.getCustomerId().trim().isEmpty()) {
            formatErrors.add("Customer ID is required");
        }

        // 验证事件数据格式
        if (event.getEventData() != null) {
            validateEventDataFormat(event.getEventType(), event.getEventData(), formatErrors);
        }

        boolean isValid = formatErrors.isEmpty();
        return new FormatValidationResult(isValid, formatErrors);
    }

    @Override
    public Map<String, Double> calculateFraudIndicators(List<SessionEvent> events, ValidationResult validationResults) {
//        Map<String, Double> indicators = new HashMap<>();
//
//        try {
//            // 2.6 时间戳异常 (TIMESTAMP_ANOMALY)
//            // 阈值：5秒，计算公式：IndicatorValue = min(timeDiffSeconds / 5, 1.0)
//            TimestampValidationResult timestampResult = validateTimestamps(events, System.currentTimeMillis());
//            double timestampAnomalyScore = calculateTimestampAnomalyScoreWithThreshold(timestampResult);
//            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.TIMESTAMP_ANOMALY.getCode(),
//                    timestampAnomalyScore);
//
//            // 2.7 事件顺序异常 (EVENT_ORDER_ANOMALY)
//            // 直接触发：若检测到任何顺序异常，IndicatorValue = 1.0
//            EventSequenceValidationResult sequenceResult = validateEventSequence(events);
//            double eventOrderAnomalyScore = sequenceResult.isValid() ? 0.0 : 1.0;
//            indicators.put("EVENT_ORDER_ANOMALY", eventOrderAnomalyScore);
//
//            // 重复事件检测（在PlaybackAnalysisService中实现）
//            DuplicateEventDetectionResult duplicateResult = detectDuplicateEvents(events);
//            double duplicateEventScore = calculateDuplicateEventScore(duplicateResult, events.size());
//            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.DUPLICATE_EVENT.getCode(),
//                    duplicateEventScore);
//
//            // 数据格式异常指标
//            double formatErrorScore = calculateFormatErrorScore(events);
//            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.INVALID_DATA_FORMAT.getCode(),
//                    formatErrorScore);
//
//        } catch (Exception e) {
//            log.error("Failed to calculate fraud indicators", e);
//            // 返回高风险指标
//            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.TIMESTAMP_ANOMALY.getCode(), 1.0);
//        }
//
//        return indicators;
        return null;
    }

    /**
     * 验证签名
     */
    private boolean verifySignature(String data, String signature, String secretKey) {
        try {
            Mac mac = Mac.getInstance(YouTubeAntiCheatConstant.DEFAULT_SIGNATURE_ALGORITHM);
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(), 
                    YouTubeAntiCheatConstant.DEFAULT_SIGNATURE_ALGORITHM);
            mac.init(keySpec);
            
            byte[] expectedSignature = mac.doFinal(data.getBytes());
            String expectedSignatureHex = bytesToHex(expectedSignature);
            
            return expectedSignatureHex.equals(signature);
        } catch (Exception e) {
            log.error("Failed to verify signature", e);
            return false;
        }
    }

    /**
     * 解密数据
     */
    private String decryptData(String encryptedData, String secretKey) {
        try {
            // TODO: 实现具体的解密逻辑
            // 这里应该使用AES等加密算法进行解密
            // 当前返回原始数据作为临时实现
            return encryptedData;
        } catch (Exception e) {
            log.error("Failed to decrypt data", e);
            return null;
        }
    }

    /**
     * 解析事件数据
     */
    private List<SessionEvent> parseEvents(String decryptedData) {
        try {
            // 尝试解析为RexyReportRequest.EventData数组（新格式）
            List<VideoReportRequest.EventData> eventDataList = JSON.parseArray(decryptedData, VideoReportRequest.EventData.class);
            if (eventDataList != null && !eventDataList.isEmpty()) {
                return eventDataList.stream()
                        .map(this::convertToSessionEvent)
                        .collect(Collectors.toList());
            }

            // 兼容旧格式：直接解析为SessionEvent数组
            return JSON.parseArray(decryptedData, SessionEvent.class);
        } catch (Exception e) {
            log.error("Failed to parse events", e);
            return new ArrayList<>();
        }
    }

    /**
     * 将RexyReportRequest.EventData转换为SessionEvent
     */
    private SessionEvent convertToSessionEvent(VideoReportRequest.EventData eventData) {
        SessionEvent sessionEvent = SessionEvent.builder()
                .id(eventData.getEventId())
                .eventType(eventData.getEventType())
                .clientTimestamp(eventData.getTimestamp())
                .sequence(eventData.getSequence())
                .serverTimestamp(System.currentTimeMillis())
                .build();

        // 转换事件数据
        Map<String, Object> eventDataMap = new HashMap<>();

        // 处理新的details结构
        if (eventData.getDetails() != null) {
            eventDataMap.putAll(convertEventDetailsToMap(eventData.getEventType(), eventData.getDetails()));
        }

        sessionEvent.setEventData(eventDataMap);
        return sessionEvent;
    }

    /**
     * 将EventDetails转换为Map
     */
    private Map<String, Object> convertEventDetailsToMap(String eventType, VideoReportRequest.EventDetails details) {
        Map<String, Object> dataMap = new HashMap<>();

        switch (eventType) {
            case "PLAYING":
                if (details.getPlayingData() != null) {
                    VideoReportRequest.PlayingEventData playingData = details.getPlayingData();
                    dataMap.put("newState", playingData.getNewState());
                    dataMap.put("currentTime", playingData.getCurrentTime());
                    dataMap.put("playbackRate", playingData.getPlaybackRate());
                }
                break;
            case "PAUSED":
                if (details.getPausedData() != null) {
                    VideoReportRequest.PausedEventData pausedData = details.getPausedData();
                    dataMap.put("newState", pausedData.getNewState());
                    dataMap.put("currentTime", pausedData.getCurrentTime());
                }
                break;
            case "SEEK":
                if (details.getSeekData() != null) {
                    VideoReportRequest.SeekEventData seekData = details.getSeekData();
                    dataMap.put("currentTime", seekData.getCurrentTime());
                    dataMap.put("previousTime", seekData.getPreviousTime());
                    dataMap.put("from", seekData.getPreviousTime());
                    dataMap.put("to", seekData.getCurrentTime());
                }
                break;
            case "FOCUS_LOST":
                if (details.getFocusLostData() != null) {
                    VideoReportRequest.FocusLostEventData focusLostData = details.getFocusLostData();
                    dataMap.put("tabActive", focusLostData.getTabActive());
                    dataMap.put("windowFocused", focusLostData.getWindowFocused());
                }
                break;
            case "FOCUS_GAINED":
                if (details.getFocusGainedData() != null) {
                    VideoReportRequest.FocusGainedEventData focusGainedData = details.getFocusGainedData();
                    dataMap.put("tabActive", focusGainedData.getTabActive());
                    dataMap.put("windowFocused", focusGainedData.getWindowFocused());
                }
                break;
            case "USER_STATE":
                if (details.getUserStateData() != null) {
                    VideoReportRequest.UserStateEventData userStateData = details.getUserStateData();
                    dataMap.put("state", userStateData.getState());
                    dataMap.put("userActivityState", userStateData.getState());
                }
                break;
        }

        return dataMap;
    }

    /**
     * 检查事件类型是否有效
     * 使用YouTubeAntiCheatConstant.EventType枚举进行验证
     */
    private boolean isValidEventType(String eventType) {
        if (eventType == null || eventType.trim().isEmpty()) {
            return false;
        }

        try {
            // 遍历所有枚举值，检查是否匹配
            for (YouTubeAntiCheatConstant.EventType type : YouTubeAntiCheatConstant.EventType.values()) {
                if (type.getCode().equals(eventType)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.warn("Failed to validate event type: {}", eventType, e);
            return false;
        }
    }

    /**
     * 验证事件详细数据结构
     */
    private ValidationResult validateEventDetails(VideoReportRequest.EventData event) {
        try {
            if (event.getDetails() == null) {
                return new ValidationResult(true, "Using legacy event data format", "SUCCESS");
            }

            String eventType = event.getEventType();
            VideoReportRequest.EventDetails details = event.getDetails();

            switch (eventType) {
                case "PLAYING":
                    return validatePlayingEventData(details.getPlayingData());
                case "PAUSED":
                    return validatePausedEventData(details.getPausedData());
                case "SEEK":
                    return validateSeekEventData(details.getSeekData());
                case "FOCUS_LOST":
                    return validateFocusLostEventData(details.getFocusLostData());
                case "FOCUS_GAINED":
                    return validateFocusGainedEventData(details.getFocusGainedData());
                case "USER_STATE":
                    return validateUserStateEventData(details.getUserStateData());
                default:
                    // 对于其他事件类型，不强制要求details
                    return new ValidationResult(true, "Event type does not require specific details validation", "SUCCESS");
            }

        } catch (Exception e) {
            log.error("Failed to validate event details for event type: {}", event.getEventType(), e);
            return new ValidationResult(false, "Event details validation failed: " + e.getMessage(), "VALIDATION_ERROR");
        }
    }

    /**
     * 验证PLAYING事件数据
     */
    private ValidationResult validatePlayingEventData(VideoReportRequest.PlayingEventData data) {
        if (data == null) {
            return new ValidationResult(false, "Playing event data is required", "MISSING_PLAYING_DATA");
        }
        if (data.getNewState() == null || data.getNewState().trim().isEmpty()) {
            return new ValidationResult(false, "New state is required for playing event", "MISSING_NEW_STATE");
        }
        if (data.getCurrentTime() == null || data.getCurrentTime() < 0) {
            return new ValidationResult(false, "Valid current time is required for playing event", "INVALID_CURRENT_TIME");
        }
        if (data.getPlaybackRate() == null || data.getPlaybackRate() <= 0) {
            return new ValidationResult(false, "Valid playback rate is required for playing event", "INVALID_PLAYBACK_RATE");
        }
        return new ValidationResult(true, "Playing event data is valid", "SUCCESS");
    }

    /**
     * 验证PAUSED事件数据
     */
    private ValidationResult validatePausedEventData(VideoReportRequest.PausedEventData data) {
        if (data == null) {
            return new ValidationResult(false, "Paused event data is required", "MISSING_PAUSED_DATA");
        }
        if (data.getNewState() == null || data.getNewState().trim().isEmpty()) {
            return new ValidationResult(false, "New state is required for paused event", "MISSING_NEW_STATE");
        }
        if (data.getCurrentTime() == null || data.getCurrentTime() < 0) {
            return new ValidationResult(false, "Valid current time is required for paused event", "INVALID_CURRENT_TIME");
        }
        return new ValidationResult(true, "Paused event data is valid", "SUCCESS");
    }

    /**
     * 验证SEEK事件数据
     */
    private ValidationResult validateSeekEventData(VideoReportRequest.SeekEventData data) {
        if (data == null) {
            return new ValidationResult(false, "Seek event data is required", "MISSING_SEEK_DATA");
        }
        if (data.getCurrentTime() == null || data.getCurrentTime() < 0) {
            return new ValidationResult(false, "Valid current time is required for seek event", "INVALID_CURRENT_TIME");
        }
        if (data.getPreviousTime() == null || data.getPreviousTime() < 0) {
            return new ValidationResult(false, "Valid previous time is required for seek event", "INVALID_PREVIOUS_TIME");
        }
        return new ValidationResult(true, "Seek event data is valid", "SUCCESS");
    }

    /**
     * 验证FOCUS_LOST事件数据
     */
    private ValidationResult validateFocusLostEventData(VideoReportRequest.FocusLostEventData data) {
        if (data == null) {
            return new ValidationResult(false, "Focus lost event data is required", "MISSING_FOCUS_LOST_DATA");
        }
        if (data.getTabActive() == null) {
            return new ValidationResult(false, "Tab active status is required for focus lost event", "MISSING_TAB_ACTIVE");
        }
        if (data.getWindowFocused() == null) {
            return new ValidationResult(false, "Window focused status is required for focus lost event", "MISSING_WINDOW_FOCUSED");
        }
        return new ValidationResult(true, "Focus lost event data is valid", "SUCCESS");
    }

    /**
     * 验证FOCUS_GAINED事件数据
     */
    private ValidationResult validateFocusGainedEventData(VideoReportRequest.FocusGainedEventData data) {
        if (data == null) {
            return new ValidationResult(false, "Focus gained event data is required", "MISSING_FOCUS_GAINED_DATA");
        }
        if (data.getTabActive() == null) {
            return new ValidationResult(false, "Tab active status is required for focus gained event", "MISSING_TAB_ACTIVE");
        }
        if (data.getWindowFocused() == null) {
            return new ValidationResult(false, "Window focused status is required for focus gained event", "MISSING_WINDOW_FOCUSED");
        }
        return new ValidationResult(true, "Focus gained event data is valid", "SUCCESS");
    }

    /**
     * 验证USER_STATE事件数据
     */
    private ValidationResult validateUserStateEventData(VideoReportRequest.UserStateEventData data) {
        if (data == null) {
            return new ValidationResult(false, "User state event data is required", "MISSING_USER_STATE_DATA");
        }
        if (data.getState() == null || data.getState().trim().isEmpty()) {
            return new ValidationResult(false, "State is required for user state event", "MISSING_STATE");
        }
        // 验证状态值是否有效
        String state = data.getState().toUpperCase();
        if (!Arrays.asList("ACTIVE", "IDLE", "LOCKED").contains(state)) {
            return new ValidationResult(false, "Invalid user state: " + data.getState(), "INVALID_USER_STATE");
        }
        return new ValidationResult(true, "User state event data is valid", "SUCCESS");
    }

    /**
     * 检查事件转换是否有效
     */
    private boolean isValidTransition(String fromEvent, String toEvent) {
        // TODO: 实现具体的事件转换规则
        // 例如：PAUSE后不能直接是ENDED，必须先PLAY
        return true; // 临时返回true
    }

    /**
     * 生成事件签名用于重复检测
     */
    private String generateEventSignature(SessionEvent event) {
        return String.format("%s_%s_%s_%d", 
                event.getEventType(), 
                event.getSessionId(), 
                event.getCustomerId(),
                event.getClientTimestamp() / 1000); // 精确到秒
    }

    /**
     * 验证事件数据格式
     */
    private void validateEventDataFormat(String eventType, Map<String, Object> eventData, 
                                       List<String> formatErrors) {
        // TODO: 根据不同的事件类型验证数据格式
        // 例如：TIMEUPDATE事件应该包含currentTime字段
    }

//    /**
//     * 计算时间戳异常分数（基于阈值设计）
//     * 使用FraudIndicatorThresholds中的计算公式
//     */
//    private double calculateTimestampAnomalyScoreWithThreshold(TimestampValidationResult result) {
//        if (result.getMaxTimeDifference() <= 0) return 0.0;
//
//        double timeDiffSeconds = result.getMaxTimeDifference() / 1000.0; // 转换为秒
//        return FraudIndicatorThresholds.calculateTimestampAnomalyScore(timeDiffSeconds);
//    }

    /**
     * 计算时间戳异常分数（原有方法保留）
     */
    private double calculateTimestampAnomalyScore(TimestampValidationResult result, int totalEvents) {
        if (totalEvents == 0) return 0.0;
        return (double) result.getAnomalousEventCount() / totalEvents;
    }

    /**
     * 计算序列异常分数
     */
    private double calculateSequenceAnomalyScore(EventSequenceValidationResult result, int totalEvents) {
        if (totalEvents == 0) return 0.0;
        return Math.min(1.0, (double) result.getSequenceErrors() / totalEvents);
    }

    /**
     * 计算重复事件分数
     */
    private double calculateDuplicateEventScore(DuplicateEventDetectionResult result, int totalEvents) {
        if (totalEvents == 0) return 0.0;
        return Math.min(1.0, (double) result.getDuplicateCount() / totalEvents);
    }

    /**
     * 计算格式错误分数
     */
    private double calculateFormatErrorScore(List<SessionEvent> events) {
        int errorCount = 0;
        for (SessionEvent event : events) {
            FormatValidationResult result = validateEventFormat(event);
            if (!result.isValid()) {
                errorCount++;
            }
        }
        return events.isEmpty() ? 0.0 : Math.min(1.0, (double) errorCount / events.size());
    }

    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
}
