package com.drex.core.service.business.rexy.impl;

import com.alibaba.fastjson2.JSON;
import com.drex.core.api.common.BusinessMonitorConstant;
import com.drex.core.api.common.RexyConstant;
import com.drex.core.api.request.GenerateMaizeRequest;
import com.drex.core.api.request.SocialConstant;
import com.drex.core.api.response.InformationDTO;
import com.drex.core.dal.tablestore.builder.MaizeRecordBuilder;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import com.drex.core.service.CoreProperties;
import com.drex.core.service.business.rexy.InformationService;
import com.drex.core.service.business.rexy.SocialCheckService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;

@Slf4j
@Service("xSocialCheckService")
public class XSocialCheckServiceImpl implements SocialCheckService {

    @Resource
    private CoreProperties coreProperties;
    @Resource
    private MaizeRecordBuilder maizeRecordBuilder;
    @Resource
    private InformationService informationService;

    @Override
    public boolean check(GenerateMaizeRequest request) {
        log.info("x check request: {},{}", request, coreProperties);
        InformationDTO informationDTO = informationService.getByLinkId(request.getSocialEventBody().getSocialContentId());
        if(informationDTO == null){
            return false;
        }
        log.info("{}::{}::{}", RexyConstant.STATISTICS_ORIGINAL_DATA, RexyConstant.RiskMonitorType.X_REPLY, JSON.toJSONString(request));

        //该x是否已经产生过金蛋
        MaizeRecord maizeRecords = maizeRecordBuilder.findMaizeRecord(request.getCustomerId(), request.getSocialEvent().name(), request.getSocialEventBody().getSocialContentId());
        if(maizeRecords != null){
            log.info("x check maizeRecord: {}", maizeRecords);
            return Boolean.FALSE;
        }
        if(!request.getSocialEvent().equals(SocialConstant.EventEnum.replay)){
            log.info("x check socialEvent: {}", request.getSocialEvent());
            return Boolean.FALSE;
        }
        if(StringUtils.isBlank(request.getSocialEventBody().getReplyContent())){
            log.info("x check replyContent: {}", request.getSocialEventBody().getReplyContent());
            return Boolean.FALSE;
        }
        if(!request.getSocialEventBody().getReplyContent().contains(coreProperties.getXHandleName())){
            log.info("x check replyContent: {}", request.getSocialEventBody().getReplyContent());
            return Boolean.FALSE;
        }
        if(request.getSocialEventBody().getReplyContent().length() < coreProperties.getXTextLength() - coreProperties.getXHandleName().length()){
            log.info("x check replyContent: {}", request.getSocialEventBody().getReplyContent());
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public String getSocialType() {
        return SocialConstant.PlatformEnum.X.name();
    }
}
