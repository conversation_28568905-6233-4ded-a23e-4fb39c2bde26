package com.drex.core.service.mapperstruct;

import com.drex.core.api.request.GenerateMaizeRequest;
import com.drex.core.api.response.MaizeDTO;
import com.drex.core.dal.tablestore.model.MaizeRecord;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MaizeMapperStruct {

    @Mapping(source = "request.customerId", target = "customerId")
    @Mapping(source = "maizeDTO.socialPlatform", target = "socialPlatform")
    @Mapping(source = "maizeDTO.socialEvent", target = "socialEvent")
    @Mapping(source = "maizeDTO.code", target = "maizeCode")
    @Mapping(source = "maizeDTO.level", target = "maizeLevel")
    @Mapping(source = "maizeDTO.score", target = "maizeScore")
    @Mapping(source = "request.socialEventBody.socialContentId", target = "socialContentId")
    @Mapping(source = "request.sessionId", target = "sessionId")
    @Mapping(target = "collectStatus", constant = "PENDING")
    @Mapping(target = "createTime", expression = "java(java.time.LocalDateTime.now().toEpochSecond( java.time.ZoneOffset.UTC))")
    @Mapping(source = "maizeDTO.expireTime", target = "expireTime")
    @Mapping(source = "maizeDTO.progress", target = "progress")
    MaizeRecord toMaizeRecord(MaizeDTO maizeDTO, GenerateMaizeRequest request);
}
