package com.drex.core.service.business.rexy;

import com.drex.core.api.common.CoreException;
import com.drex.core.api.request.CollectMaizeRequest;
import com.drex.core.api.request.GenerateMaizeRequest;
import com.drex.core.api.request.LastMaizeRequest;
import com.drex.core.api.response.MaizeDTO;

import java.util.Base64;
import java.util.List;

public interface MaizeService {

    MaizeDTO generateMaize(GenerateMaizeRequest request);

    List<MaizeDTO> lastMaize(LastMaizeRequest request);

    MaizeDTO collectMaize(CollectMaizeRequest maizeCode)  throws CoreException;
}
