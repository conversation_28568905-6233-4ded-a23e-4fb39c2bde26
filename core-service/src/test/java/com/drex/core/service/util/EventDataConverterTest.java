package com.drex.core.service.util;

import com.drex.core.api.request.SocialConstant;
import com.drex.core.api.request.VideoReportRequest;
import com.drex.core.service.cache.model.SessionEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 事件数据转换器测试
 */
@ExtendWith(MockitoExtension.class)
class EventDataConverterTest {

    @InjectMocks
    private EventDataConverter eventDataConverter;

    private VideoReportRequest.EventData playingEventData;
    private VideoReportRequest.EventData pausedEventData;
    private VideoReportRequest.EventData seekEventData;
    private VideoReportRequest.EventData focusLostEventData;

    @BeforeEach
    void setUp() {
        // 创建PLAYING事件数据
        VideoReportRequest.PlayingEventData playingData = VideoReportRequest.PlayingEventData.builder()
                .newState("PLAYING")
                .currentTime(120.5)
                .playbackRate(1.0)
                .build();

        VideoReportRequest.EventDetails playingDetails = VideoReportRequest.EventDetails.builder()
                .playingData(playingData)
                .build();

        playingEventData = VideoReportRequest.EventData.builder()
                .eventId("event-001")
                .eventType("PLAYING")
                .timestamp(System.currentTimeMillis())
                .sequence(1)
                .details(playingDetails)
                .build();

        // 创建PAUSED事件数据
        VideoReportRequest.PausedEventData pausedData = VideoReportRequest.PausedEventData.builder()
                .newState("PAUSED")
                .currentTime(125.0)
                .build();

        VideoReportRequest.EventDetails pausedDetails = VideoReportRequest.EventDetails.builder()
                .pausedData(pausedData)
                .build();

        pausedEventData = VideoReportRequest.EventData.builder()
                .eventId("event-002")
                .eventType("PAUSED")
                .timestamp(System.currentTimeMillis())
                .sequence(2)
                .details(pausedDetails)
                .build();

        // 创建SEEK事件数据
        VideoReportRequest.SeekEventData seekData = VideoReportRequest.SeekEventData.builder()
                .currentTime(150.0)
                .previousTime(125.0)
                .build();

        VideoReportRequest.EventDetails seekDetails = VideoReportRequest.EventDetails.builder()
                .seekData(seekData)
                .build();

        seekEventData = VideoReportRequest.EventData.builder()
                .eventId("event-003")
                .eventType("SEEK")
                .timestamp(System.currentTimeMillis())
                .sequence(3)
                .details(seekDetails)
                .build();

        // 创建FOCUS_LOST事件数据
        VideoReportRequest.FocusLostEventData focusLostData = VideoReportRequest.FocusLostEventData.builder()
                .tabActive(false)
                .windowFocused(false)
                .build();

        VideoReportRequest.EventDetails focusLostDetails = VideoReportRequest.EventDetails.builder()
                .focusLostData(focusLostData)
                .build();

        focusLostEventData = VideoReportRequest.EventData.builder()
                .eventId("event-004")
                .eventType("FOCUS_LOST")
                .timestamp(System.currentTimeMillis())
                .sequence(4)
                .details(focusLostDetails)
                .build();
    }

    @Test
    void testConvertPlayingEventToSessionEvent() {
        // When
        SessionEvent sessionEvent = eventDataConverter.convertToSessionEvent(
                playingEventData,
                "session-123",
                "customer-456",
                SocialConstant.PlatformEnum.YouTube,
                SocialConstant.EventEnum.watch,
                "device-finger-789",
                "***********"
        );

        // Then
        assertNotNull(sessionEvent);
        assertEquals("event-001", sessionEvent.getId());
        assertEquals("PLAYING", sessionEvent.getEventType());
        assertEquals("session-123", sessionEvent.getSessionId());
        assertEquals("customer-456", sessionEvent.getCustomerId());
        assertEquals(SocialConstant.PlatformEnum.YouTube, sessionEvent.getSocialPlatform());
        assertEquals(SocialConstant.EventEnum.watch, sessionEvent.getSocialEvent());
        assertEquals("device-finger-789", sessionEvent.getDeviceFinger());
        assertEquals("***********", sessionEvent.getSourceIp());
        assertEquals(Integer.valueOf(1), sessionEvent.getSequence());

        // 验证事件详细数据
        assertNotNull(sessionEvent.getDetails());
        assertNotNull(sessionEvent.getDetails().getPlayingData());
        assertEquals("PLAYING", sessionEvent.getDetails().getPlayingData().getNewState());
        assertEquals(120.5, sessionEvent.getDetails().getPlayingData().getCurrentTime());
        assertEquals(1.0, sessionEvent.getDetails().getPlayingData().getPlaybackRate());

        // 验证向后兼容的eventData字段
        assertNotNull(sessionEvent.getEventData());
        assertEquals("PLAYING", sessionEvent.getEventData().get("newState"));
        assertEquals(120.5, sessionEvent.getEventData().get("currentTime"));
        assertEquals(1.0, sessionEvent.getEventData().get("playbackRate"));
    }

    @Test
    void testConvertPausedEventToSessionEvent() {
        // When
        SessionEvent sessionEvent = eventDataConverter.convertToSessionEvent(
                pausedEventData,
                "session-123",
                "customer-456",
                SocialConstant.PlatformEnum.YouTube,
                SocialConstant.EventEnum.watch,
                "device-finger-789",
                "***********"
        );

        // Then
        assertNotNull(sessionEvent);
        assertEquals("PAUSED", sessionEvent.getEventType());
        assertNotNull(sessionEvent.getDetails().getPausedData());
        assertEquals("PAUSED", sessionEvent.getDetails().getPausedData().getNewState());
        assertEquals(125.0, sessionEvent.getDetails().getPausedData().getCurrentTime());
    }

    @Test
    void testConvertSeekEventToSessionEvent() {
        // When
        SessionEvent sessionEvent = eventDataConverter.convertToSessionEvent(
                seekEventData,
                "session-123",
                "customer-456",
                SocialConstant.PlatformEnum.YouTube,
                SocialConstant.EventEnum.watch,
                "device-finger-789",
                "***********"
        );

        // Then
        assertNotNull(sessionEvent);
        assertEquals("SEEK", sessionEvent.getEventType());
        assertNotNull(sessionEvent.getDetails().getSeekData());
        assertEquals(150.0, sessionEvent.getDetails().getSeekData().getCurrentTime());
        assertEquals(125.0, sessionEvent.getDetails().getSeekData().getPreviousTime());
    }

    @Test
    void testConvertFocusLostEventToSessionEvent() {
        // When
        SessionEvent sessionEvent = eventDataConverter.convertToSessionEvent(
                focusLostEventData,
                "session-123",
                "customer-456",
                SocialConstant.PlatformEnum.YouTube,
                SocialConstant.EventEnum.watch,
                "device-finger-789",
                "***********"
        );

        // Then
        assertNotNull(sessionEvent);
        assertEquals("FOCUS_LOST", sessionEvent.getEventType());
        assertNotNull(sessionEvent.getDetails().getFocusLostData());
        assertEquals(false, sessionEvent.getDetails().getFocusLostData().getTabActive());
        assertEquals(false, sessionEvent.getDetails().getFocusLostData().getWindowFocused());
    }

    @Test
    void testConvertMultipleEventsToSessionEvents() {
        // Given
        List<VideoReportRequest.EventData> eventDataList = Arrays.asList(
                playingEventData, pausedEventData, seekEventData, focusLostEventData
        );

        // When
        List<SessionEvent> sessionEvents = eventDataConverter.convertToSessionEvents(
                eventDataList,
                "session-123",
                "customer-456",
                SocialConstant.PlatformEnum.YouTube,
                SocialConstant.EventEnum.watch,
                "device-finger-789",
                "***********"
        );

        // Then
        assertNotNull(sessionEvents);
        assertEquals(4, sessionEvents.size());

        // 验证每个事件的基本信息
        for (int i = 0; i < sessionEvents.size(); i++) {
            SessionEvent sessionEvent = sessionEvents.get(i);
            assertEquals("session-123", sessionEvent.getSessionId());
            assertEquals("customer-456", sessionEvent.getCustomerId());
            assertEquals(SocialConstant.PlatformEnum.YouTube, sessionEvent.getSocialPlatform());
            assertEquals(SocialConstant.EventEnum.watch, sessionEvent.getSocialEvent());
            assertEquals(i + 1, sessionEvent.getSequence().intValue());
        }

        // 验证事件类型
        assertEquals("PLAYING", sessionEvents.get(0).getEventType());
        assertEquals("PAUSED", sessionEvents.get(1).getEventType());
        assertEquals("SEEK", sessionEvents.get(2).getEventType());
        assertEquals("FOCUS_LOST", sessionEvents.get(3).getEventType());
    }

    @Test
    void testConvertSessionEventBackToRequestEventData() {
        // Given
        SessionEvent sessionEvent = eventDataConverter.convertToSessionEvent(
                playingEventData,
                "session-123",
                "customer-456",
                SocialConstant.PlatformEnum.YouTube,
                SocialConstant.EventEnum.watch,
                "device-finger-789",
                "***********"
        );

        // When
        VideoReportRequest.EventData convertedBack = eventDataConverter.convertToRequestEventData(sessionEvent);

        // Then
        assertNotNull(convertedBack);
        assertEquals(playingEventData.getEventId(), convertedBack.getEventId());
        assertEquals(playingEventData.getEventType(), convertedBack.getEventType());
        assertEquals(playingEventData.getSequence(), convertedBack.getSequence());
        
        // 验证详细数据
        assertNotNull(convertedBack.getDetails());
        assertNotNull(convertedBack.getDetails().getPlayingData());
        assertEquals("PLAYING", convertedBack.getDetails().getPlayingData().getNewState());
        assertEquals(120.5, convertedBack.getDetails().getPlayingData().getCurrentTime());
        assertEquals(1.0, convertedBack.getDetails().getPlayingData().getPlaybackRate());
    }

    @Test
    void testConvertEventWithNullDetails() {
        // Given
        VideoReportRequest.EventData eventDataWithoutDetails = VideoReportRequest.EventData.builder()
                .eventId("event-null")
                .eventType("UNKNOWN")
                .timestamp(System.currentTimeMillis())
                .sequence(1)
                .details(null)
                .build();

        // When
        SessionEvent sessionEvent = eventDataConverter.convertToSessionEvent(
                eventDataWithoutDetails,
                "session-123",
                "customer-456",
                SocialConstant.PlatformEnum.YouTube,
                SocialConstant.EventEnum.watch,
                "device-finger-789",
                "***********"
        );

        // Then
        assertNotNull(sessionEvent);
        assertEquals("UNKNOWN", sessionEvent.getEventType());
        assertNull(sessionEvent.getDetails());
        assertNotNull(sessionEvent.getEventData());
        assertTrue(sessionEvent.getEventData().isEmpty());
    }
}
