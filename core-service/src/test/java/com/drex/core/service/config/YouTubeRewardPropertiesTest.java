package com.drex.core.service.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * YouTube奖励配置动态进度计算测试
 */
class YouTubeRewardPropertiesTest {

    private YouTubeRewardProperties youTubeRewardProperties;

    @BeforeEach
    void setUp() {
        youTubeRewardProperties = new YouTubeRewardProperties();
        
        // 配置长视频奖励阶段（4个阶段）
        YouTubeRewardProperties.VideoRewardConfig longVideoConfig = new YouTubeRewardProperties.VideoRewardConfig();
        longVideoConfig.setVideoType("LONG");
        longVideoConfig.setEnabled(true);
        
        YouTubeRewardProperties.ProgressStage stage1 = new YouTubeRewardProperties.ProgressStage();
        stage1.setStage(1);
        stage1.setStageName("第一阶段");
        stage1.setMinWatchPercentage(0.2);
        stage1.setMaxWatchPercentage(0.4);
        stage1.setRewardAmount(100L);
        stage1.setEnabled(true);
        
        YouTubeRewardProperties.ProgressStage stage2 = new YouTubeRewardProperties.ProgressStage();
        stage2.setStage(2);
        stage2.setStageName("第二阶段");
        stage2.setMinWatchPercentage(0.5);
        stage2.setMaxWatchPercentage(0.7);
        stage2.setRewardAmount(200L);
        stage2.setEnabled(true);
        
        YouTubeRewardProperties.ProgressStage stage3 = new YouTubeRewardProperties.ProgressStage();
        stage3.setStage(3);
        stage3.setStageName("第三阶段");
        stage3.setMinWatchPercentage(0.8);
        stage3.setMaxWatchPercentage(1.2);
        stage3.setRewardAmount(300L);
        stage3.setEnabled(true);
        
        longVideoConfig.setStages(Arrays.asList(stage1, stage2, stage3));
        youTubeRewardProperties.setLongVideo(longVideoConfig);
        
        // 配置短视频奖励阶段（1个阶段）
        YouTubeRewardProperties.VideoRewardConfig shortVideoConfig = new YouTubeRewardProperties.VideoRewardConfig();
        shortVideoConfig.setVideoType("SHORT");
        shortVideoConfig.setEnabled(true);
        
        YouTubeRewardProperties.ProgressStage shortStage = new YouTubeRewardProperties.ProgressStage();
        shortStage.setStage(1);
        shortStage.setStageName("短视频奖励");
        shortStage.setMinWatchPercentage(0.8);
        shortStage.setMaxWatchPercentage(1.5);
        shortStage.setRewardAmount(150L);
        shortStage.setEnabled(true);
        
        shortVideoConfig.setStages(Arrays.asList(shortStage));
        youTubeRewardProperties.setShortVideo(shortVideoConfig);
    }

    @Test
    void testIsShortVideo() {
        // 测试短视频URL识别
        assertTrue(youTubeRewardProperties.isShortVideo("https://www.youtube.com/shorts/abc123"));
        assertTrue(youTubeRewardProperties.isShortVideo("https://youtube.com/shorts/xyz789"));
        assertTrue(youTubeRewardProperties.isShortVideo("https://m.youtube.com/shorts/test"));
        
        // 测试长视频URL识别
        assertFalse(youTubeRewardProperties.isShortVideo("https://www.youtube.com/watch?v=abc123"));
        assertFalse(youTubeRewardProperties.isShortVideo("https://youtube.com/watch?v=xyz789"));
        assertFalse(youTubeRewardProperties.isShortVideo(""));
        assertFalse(youTubeRewardProperties.isShortVideo(null));
    }

    @Test
    void testGetVideoRewardConfigByUrl() {
        // 测试短视频配置获取
        YouTubeRewardProperties.VideoRewardConfig shortConfig = 
                youTubeRewardProperties.getVideoRewardConfigByUrl("https://www.youtube.com/shorts/abc123");
        assertEquals("SHORT", shortConfig.getVideoType());
        assertEquals(1, shortConfig.getStages().size());
        
        // 测试长视频配置获取
        YouTubeRewardProperties.VideoRewardConfig longConfig = 
                youTubeRewardProperties.getVideoRewardConfigByUrl("https://www.youtube.com/watch?v=abc123");
        assertEquals("LONG", longConfig.getVideoType());
        assertEquals(3, longConfig.getStages().size());
    }

    @Test
    void testCalculateDynamicProgressForLongVideo() {
        String longVideoUrl = "https://www.youtube.com/watch?v=abc123";
        
        // 测试未达到任何阶段
        assertNull(youTubeRewardProperties.calculateDynamicProgress(0.1, longVideoUrl));
        
        // 测试达到第一阶段最小要求
        assertEquals(1, youTubeRewardProperties.calculateDynamicProgress(0.2, longVideoUrl));
        assertEquals(1, youTubeRewardProperties.calculateDynamicProgress(0.3, longVideoUrl));
        assertEquals(1, youTubeRewardProperties.calculateDynamicProgress(0.4, longVideoUrl));
        
        // 测试超过第一阶段，进入第二阶段
        assertEquals(2, youTubeRewardProperties.calculateDynamicProgress(0.5, longVideoUrl));
        assertEquals(2, youTubeRewardProperties.calculateDynamicProgress(0.6, longVideoUrl));
        assertEquals(2, youTubeRewardProperties.calculateDynamicProgress(0.7, longVideoUrl));
        
        // 测试超过第二阶段，进入第三阶段
        assertEquals(3, youTubeRewardProperties.calculateDynamicProgress(0.8, longVideoUrl));
        assertEquals(3, youTubeRewardProperties.calculateDynamicProgress(1.0, longVideoUrl));
        assertEquals(3, youTubeRewardProperties.calculateDynamicProgress(1.2, longVideoUrl));
        
        // 测试超过所有阶段
        assertEquals(3, youTubeRewardProperties.calculateDynamicProgress(1.5, longVideoUrl));
    }

    @Test
    void testCalculateDynamicProgressForShortVideo() {
        String shortVideoUrl = "https://www.youtube.com/shorts/abc123";
        
        // 测试未达到阶段要求
        assertNull(youTubeRewardProperties.calculateDynamicProgress(0.7, shortVideoUrl));
        
        // 测试达到短视频阶段要求
        assertEquals(1, youTubeRewardProperties.calculateDynamicProgress(0.8, shortVideoUrl));
        assertEquals(1, youTubeRewardProperties.calculateDynamicProgress(1.0, shortVideoUrl));
        assertEquals(1, youTubeRewardProperties.calculateDynamicProgress(1.5, shortVideoUrl));
        
        // 测试超过短视频阶段
        assertEquals(1, youTubeRewardProperties.calculateDynamicProgress(2.0, shortVideoUrl));
    }

    @Test
    void testCalculateDynamicProgressEdgeCases() {
        String longVideoUrl = "https://www.youtube.com/watch?v=abc123";
        
        // 测试null值
        assertNull(youTubeRewardProperties.calculateDynamicProgress(null, longVideoUrl));
        
        // 测试边界值
        assertEquals(1, youTubeRewardProperties.calculateDynamicProgress(0.2, longVideoUrl)); // 刚好达到第一阶段最小值
        assertEquals(2, youTubeRewardProperties.calculateDynamicProgress(0.41, longVideoUrl)); // 刚好超过第一阶段最大值
        assertEquals(2, youTubeRewardProperties.calculateDynamicProgress(0.5, longVideoUrl)); // 刚好达到第二阶段最小值
        assertEquals(3, youTubeRewardProperties.calculateDynamicProgress(0.71, longVideoUrl)); // 刚好超过第二阶段最大值
    }

    @Test
    void testCalculateDynamicProgressWithDisabledStages() {
        // 创建一个有禁用阶段的配置
        YouTubeRewardProperties.VideoRewardConfig config = new YouTubeRewardProperties.VideoRewardConfig();
        config.setVideoType("LONG");
        config.setEnabled(true);
        
        YouTubeRewardProperties.ProgressStage stage1 = new YouTubeRewardProperties.ProgressStage();
        stage1.setStage(1);
        stage1.setMinWatchPercentage(0.2);
        stage1.setMaxWatchPercentage(0.4);
        stage1.setEnabled(true);
        
        YouTubeRewardProperties.ProgressStage stage2 = new YouTubeRewardProperties.ProgressStage();
        stage2.setStage(2);
        stage2.setMinWatchPercentage(0.5);
        stage2.setMaxWatchPercentage(0.7);
        stage2.setEnabled(false); // 禁用第二阶段
        
        YouTubeRewardProperties.ProgressStage stage3 = new YouTubeRewardProperties.ProgressStage();
        stage3.setStage(3);
        stage3.setMinWatchPercentage(0.8);
        stage3.setMaxWatchPercentage(1.2);
        stage3.setEnabled(true);
        
        config.setStages(Arrays.asList(stage1, stage2, stage3));
        youTubeRewardProperties.setLongVideo(config);
        
        String longVideoUrl = "https://www.youtube.com/watch?v=abc123";
        
        // 第一阶段正常
        assertEquals(1, youTubeRewardProperties.calculateDynamicProgress(0.3, longVideoUrl));
        
        // 跳过禁用的第二阶段，直接进入第三阶段
        assertEquals(2, youTubeRewardProperties.calculateDynamicProgress(0.8, longVideoUrl));
    }
}
