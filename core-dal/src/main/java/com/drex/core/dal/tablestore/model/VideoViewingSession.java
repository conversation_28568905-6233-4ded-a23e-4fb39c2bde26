package com.drex.core.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import com.drex.core.dal.tablestore.constant.Constant;
import lombok.Data;

import java.io.Serializable;

/**
 * 视频观看会话表
 * 记录用户单次观看视频的完整过程
 */
@Table(name = Constant.TABLE_NAME_VIDEO_VIEWING_SESSION)
@Data
public class VideoViewingSession implements Serializable {

    @PartitionKey(name = Constant.COLUMN_NAME_SESSION_ID, type = PartitionKey.Type.STRING)
    private String sessionId;

    @Column(name = Constant.COLUMN_NAME_CUSTOMER_ID, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_VIDEO_VIEWING_SESSION, column = Constant.COLUMN_NAME_CUSTOMER_ID)
    private String customerId;

    @Column(name = Constant.COLUMN_NAME_VIDEO_ID, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_VIDEO_VIEWING_SESSION, column = Constant.COLUMN_NAME_VIDEO_ID)
    private String videoId;

    @Column(name = Constant.COLUMN_NAME_VIDEO_URL, isDefined = true)
    private String videoUrl;

    @Column(name = Constant.COLUMN_NAME_VIDEO_DURATION_SECONDS, isDefined = true)
    private Integer videoDurationSeconds;

    @Column(name = Constant.COLUMN_NAME_START_TIME, isDefined = true)
    private Long startTime;

    @Column(name = Constant.COLUMN_NAME_SERVER_START_TIME, isDefined = true)
    private Long serverStartTime;

    @Column(name = Constant.COLUMN_NAME_EXPIRED_TIME, isDefined = true)
    private Long expiredTime;

    @Column(name = Constant.COLUMN_NAME_SERVER_END_TIME, isDefined = true)
    private Long serverEndTime;

    @Column(name = Constant.COLUMN_NAME_SESSION_STATUS, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_VIDEO_VIEWING_SESSION, column = Constant.COLUMN_NAME_SESSION_STATUS)
    private String sessionStatus;

    @Column(name = Constant.COLUMN_NAME_FINAL_RISK_SCORE, type = Column.Type.DOUBLE, isDefined = true)
    private Double finalRiskScore;

    @Column(name = Constant.COLUMN_NAME_CLIENT_REPORTED_WATCH_SECONDS, isDefined = true)
    private Long clientReportedWatchSeconds;

    @Column(name = Constant.COLUMN_NAME_CALCULATED_ENGAGED_SECONDS, isDefined = true)
    private Long calculatedEngagedSeconds;

    @Column(name = Constant.COLUMN_NAME_REWARD_GRANTED, isDefined = true)
    private Boolean rewardGranted;

    @Column(name = Constant.COLUMN_NAME_DENIAL_REASON, isDefined = true)
    private String denialReason;

    @Column(name = Constant.COLUMN_NAME_CLIENT_IP, isDefined = true)
    private String clientIp;

    @Column(name = Constant.COLUMN_NAME_CREATED, isDefined = true)
    @SearchIndex(name = Constant.SEARCH_INDEX_VIDEO_VIEWING_SESSION, column = Constant.COLUMN_NAME_CREATED, fieldType = FieldType.LONG)
    private Long created;

    @Column(name = Constant.COLUMN_NAME_MODIFIED, isDefined = true)
    private Long modified;
}
