package com.drex.core.dal.tablestore.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.*;
import lombok.Data;

import java.io.Serializable;

@Data
@Table(name = "maize_record")
public class MaizeRecord implements Serializable {

    public static final String SEARCH_MAIZE_RECORD = "idx_maize_record";

    @PartitionKey(name = "maize_code")
    @SearchIndex(name = SEARCH_MAIZE_RECORD, column = "maize_code")
    private String maizeCode;

    @Column(name = "customer_id")
    @SearchIndex(name = SEARCH_MAIZE_RECORD, column = "customer_id")
    private String customerId;

    @Column(name = "social_platform")
    @SearchIndex(name = SEARCH_MAIZE_RECORD, column = "social_platform")
    private String socialPlatform;

    @Column(name = "social_event")
    @SearchIndex(name = SEARCH_MAIZE_RECORD, column = "social_event")
    private String socialEvent;

    @Column(name = "social_content_id")
    @SearchIndex(name = SEARCH_MAIZE_RECORD, column = "social_content_id")
    private String socialContentId;

    @Column(name = "maize_level")
    @SearchIndex(name = SEARCH_MAIZE_RECORD, column = "maize_level")
    private String maizeLevel;

    @Column(name = "maize_score", type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_MAIZE_RECORD, column = "maize_score", fieldType = FieldType.LONG)
    private Long maizeScore;

    @Column(name = "session_id")
    @SearchIndex(name = SEARCH_MAIZE_RECORD, column = "session_id")
    private String sessionId;

    @Column(name = "progress", type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_MAIZE_RECORD, column = "progress", fieldType = FieldType.LONG)
    private Integer progress;

    @Column(name = "collect_status")
    @SearchIndex(name = SEARCH_MAIZE_RECORD, column = "collect_status")
    private String collectStatus;

    @Column(name = "create_time", type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_MAIZE_RECORD, column = "create_time", fieldType = FieldType.LONG)
    private Long createTime;

    @Column(name = "expire_time", type = Column.Type.INTEGER)
    @SearchIndex(name = SEARCH_MAIZE_RECORD, column = "expire_time", fieldType = FieldType.LONG)
    private Long expireTime;

    @Column(name = "status")
    @SearchIndex(name = SEARCH_MAIZE_RECORD, column = "status")
    private String status;
}
