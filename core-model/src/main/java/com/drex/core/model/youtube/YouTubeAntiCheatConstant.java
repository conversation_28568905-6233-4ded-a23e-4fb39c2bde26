package com.drex.core.model.youtube;

import lombok.Getter;

/**
 * YouTube防刷系统常量定义
 */
public class YouTubeAntiCheatConstant {

    /**
     * 会话状态枚举
     */
    @Getter
    public enum SessionStatus {
        IN_PROGRESS("IN_PROGRESS", "进行中"),
        COMPLETED_PENDING_VALIDATION("COMPLETED_PENDING_VALIDATION", "完成待验证"),
        VALIDATED_PASSED("VALIDATED_PASSED", "验证通过"),
        VALIDATED_FAILED("VALIDATED_FAILED", "验证失败"),
        EXPIRED("EXPIRED", "已过期"),
        CANCELLED("CANCELLED", "已取消");

        private final String code;
        private final String description;

        SessionStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }

    /**
     * 事件类型枚举
     */
    @Getter
    public enum EventType {
        // 原有事件类型
        PLAY("PLAY", "播放"),
        PAUSE("PAUSE", "暂停"),
        SEEKING("SEEKING", "跳转"),
        SEEKED("SEEKED", "跳转完成"),
        TIMEUPDATE("TIMEUPDATE", "时间更新"),
        ENDED("ENDED", "播放结束"),
        FOCUS("FOCUS", "获得焦点"),
        BLUR("BLUR", "失去焦点"),
        VISIBILITY_CHANGE("VISIBILITY_CHANGE", "可见性变化"),
        PLAYBACK_RATE_CHANGE("PLAYBACK_RATE_CHANGE", "播放速率变化"),
        VOLUME_CHANGE("VOLUME_CHANGE", "音量变化"),
        FULLSCREEN_CHANGE("FULLSCREEN_CHANGE", "全屏状态变化"),
        RESIZE("RESIZE", "窗口大小变化"),
        MOUSE_MOVE("MOUSE_MOVE", "鼠标移动"),
        KEY_PRESS("KEY_PRESS", "按键"),
        SCROLL("SCROLL", "滚动"),

        // 新增的6种事件类型
        PLAYING("PLAYING", "播放状态事件"),
        PAUSED("PAUSED", "暂停状态事件"),
        SEEK("SEEK", "跳转事件"),
        FOCUS_LOST("FOCUS_LOST", "失焦事件"),
        FOCUS_GAINED("FOCUS_GAINED", "聚焦事件"),
        USER_STATE("USER_STATE", "用户状态事件");

        private final String code;
        private final String description;

        EventType(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }

    /**
     * 欺诈指标类型枚举
     */
    @Getter
    public enum FraudIndicatorType {
        // 数据验证类
        TIMESTAMP_ANOMALY("TIMESTAMP_ANOMALY", "时间戳异常"),
        EVENT_SEQUENCE_ANOMALY("EVENT_SEQUENCE_ANOMALY", "事件顺序异常"),
        DUPLICATE_EVENT("DUPLICATE_EVENT", "重复事件"),
        INVALID_DATA_FORMAT("INVALID_DATA_FORMAT", "数据格式无效"),

        // 播放分析类
        EXCESSIVE_PLAYBACK_SPEED("EXCESSIVE_PLAYBACK_SPEED", "异常播放速率"),
        ABNORMAL_SEEKING("ABNORMAL_SEEKING", "异常跳转"),
        COMPLETION_PERCENTAGE_ANOMALY("COMPLETION_PERCENTAGE_ANOMALY", "完成百分比异常"),
        PLAYBACK_PATTERN_ANOMALY("PLAYBACK_PATTERN_ANOMALY", "播放模式异常"),

        // 焦点和活动类
        LOW_FOCUS_PERCENTAGE("LOW_FOCUS_PERCENTAGE", "低聚焦百分比"),
        LONG_IDLE_TIME("LONG_IDLE_TIME", "长时间空闲"),
        ACTIVITY_STATE_INCONSISTENCY("ACTIVITY_STATE_INCONSISTENCY", "活动状态不一致"),
        FOCUS_PATTERN_ANOMALY("FOCUS_PATTERN_ANOMALY", "焦点模式异常"),

        // 环境分析类
        FINGERPRINT_DUPLICATION("FINGERPRINT_DUPLICATION", "指纹重复"),
        MALICIOUS_IP("MALICIOUS_IP", "恶意IP"),
        ENVIRONMENT_INCONSISTENCY("ENVIRONMENT_INCONSISTENCY", "环境不一致"),
        SUSPICIOUS_USER_AGENT("SUSPICIOUS_USER_AGENT", "可疑用户代理"),
        GEO_LOCATION_ANOMALY("GEO_LOCATION_ANOMALY", "地理位置异常");

        private final String code;
        private final String description;

        FraudIndicatorType(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }

    /**
     * 奖励阶段枚举
     */
    @Getter
    public enum RewardStage {
        STAGE_1("STAGE_1", "第一阶段", 0.2, 0.4),
        STAGE_2("STAGE_2", "第二阶段", 0.5, 0.7),
        STAGE_3("STAGE_3", "第三阶段", 0.8, 1.2);

        private final String code;
        private final String description;
        private final Double watchMinPercentage;
        private final Double watchMaxPercentage;

        RewardStage(String code, String description, Double watchMinPercentage, Double watchMaxPercentage) {
            this.code = code;
            this.description = description;
            this.watchMinPercentage = watchMinPercentage;
            this.watchMaxPercentage = watchMaxPercentage;
        }
    }

    /**
     * 用户活动状态枚举
     */
    @Getter
    public enum UserActivityState {
        ACTIVE("active", "活跃"),
        IDLE("idle", "空闲"),
        LOCKED("locked", "锁定");

        private final String code;
        private final String description;

        UserActivityState(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }

    /**
     * IP类型枚举
     */
    @Getter
    public enum IpType {
        RESIDENTIAL("RESIDENTIAL", "住宅IP"),
        DATACENTER("DATACENTER", "数据中心IP"),
        PROXY("PROXY", "代理IP"),
        VPN("VPN", "VPN IP"),
        TOR("TOR", "Tor出口节点"),
        MOBILE("MOBILE", "移动IP"),
        UNKNOWN("UNKNOWN", "未知类型");

        private final String code;
        private final String description;

        IpType(String code, String description) {
            this.code = code;
            this.description = description;
        }
    }

    // 常量定义
    public static final String SESSION_CACHE_PREFIX = "youtube_session:";
    public static final String REWARD_CODE_CACHE_PREFIX = "reward_code:";
    public static final String USER_BLACKLIST_CACHE_PREFIX = "user_blacklist:";
    public static final String IP_BLACKLIST_CACHE_PREFIX = "ip_blacklist:";
    
    public static final int DEFAULT_SESSION_TIMEOUT_MINUTES = 60;
    public static final int DEFAULT_MAX_EVENTS_PER_SESSION = 10000;
    public static final double DEFAULT_MIN_WATCH_PERCENTAGE = 0.7;
    public static final double DEFAULT_TRUST_SCORE_THRESHOLD = 0.6;
    public static final int DEFAULT_FINGERPRINT_THRESHOLD = 3;
    
    // 时间常量（毫秒）
    public static final long ONE_MINUTE_MS = 60 * 1000L;
    public static final long ONE_HOUR_MS = 60 * ONE_MINUTE_MS;
    public static final long ONE_DAY_MS = 24 * ONE_HOUR_MS;
    
    // 事件上报间隔计算公式相关常量
    public static final int BASE_REPORT_INTERVAL = 10; // 基础间隔10秒
    public static final int MAX_ADDITIONAL_INTERVAL = 10; // 最大额外间隔10秒
    public static final int INTERVAL_CALCULATION_DIVISOR = 99; // 计算除数

    // API接口路径常量
    public static final String API_VIDEO_SESSION_INIT = "video/session/init";
    public static final String API_REXY_REPORT = "rexy/report";
    public static final String API_TASK_SOCIAL_EVENT = "task/socialEvent";
    public static final String API_REWARD_COLLECT = "reward/collect";

    // 加密和签名相关常量
    public static final String DEFAULT_ENCRYPTION_ALGORITHM = "AES/CBC/PKCS5Padding";
    public static final String DEFAULT_SIGNATURE_ALGORITHM = "HmacSHA256";
    public static final int DEFAULT_KEY_LENGTH = 256;
    public static final int DEFAULT_IV_LENGTH = 16;

    // 缓存过期时间常量
    public static final long SESSION_CACHE_EXPIRE_SECONDS = 24 * 60 * 60; // 24小时
    public static final long REWARD_CODE_EXPIRE_SECONDS = 30 * 60; // 30分钟
    public static final long BLACKLIST_CACHE_EXPIRE_SECONDS = 7 * 24 * 60 * 60; // 7天
    public static final long IP_REPUTATION_CACHE_EXPIRE_SECONDS = 24 * 60 * 60; // 24小时

    // 限制常量
    public static final int MAX_SESSION_EVENTS = 10000; // 单个会话最大事件数
    public static final int MAX_REPORT_EVENTS_PER_REQUEST = 100; // 单次上报最大事件数
    public static final int MAX_SESSION_DURATION_HOURS = 24; // 最大会话持续时间
    public static final int MAX_CONCURRENT_SESSIONS_PER_USER = 5; // 用户最大并发会话数
}
